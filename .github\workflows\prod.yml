name: Prod - Deploy

# Controls when the action will run.
on:
  # Triggers the workflow on push or pull request events but only for the master branch
  push:
    branches:
      - master
  pull_request:
    types:
      - closed
    branches:
      - master

jobs:
  if_merged:
    if: github.event.pull_request.merged == true
    name: 🎉 Deploy
    runs-on: ubuntu-latest

    steps:
      - name: 🚚 Get latest code
        uses: actions/checkout@v2

      - name: Install Node.js 20
        uses: actions/setup-node@v1
        with:
          node-version: '20.x'

      - name: 🔨 Build Project
        run: |
          yarn
          yarn build-prod

      - name: 📂 Deploy to Server
        uses: easingthemes/ssh-deploy@v2.1.5
        env:
          SSH_PRIVATE_KEY: ${{ secrets.SERVER_SSH_KEY }}
          # ARGS: "-rltgoDzvO --delete"
          SOURCE: 'dist/'
          REMOTE_HOST: **************
          REMOTE_USER: u778408432
          REMOTE_PORT: '65002'
          TARGET: domains/codedthemes.com/public_html/demos/admin-templates/datta-able/angular/free
          EXCLUDE: '/node_modules/'
