{"name": "smart_screen_control_frontend", "version": "6.0.0", "author": "CodedThemes", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build-prod": "ng build --configuration production --base-href /demos/admin-templates/datta-able/angular/free/", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint", "lint:fix": "ng lint --fix", "prettier": "prettier --write ./src"}, "private": false, "dependencies": {"@angular/animations": "19.0.5", "@angular/cdk": "19.0.4", "@angular/common": "19.0.5", "@angular/compiler": "19.0.5", "@angular/core": "19.0.5", "@angular/forms": "19.0.5", "@angular/localize": "19.0.5", "@angular/material": "^19.0.4", "@angular/platform-browser": "19.0.5", "@angular/platform-browser-dynamic": "19.0.5", "@angular/router": "19.0.5", "@ng-bootstrap/ng-bootstrap": "18.0.0", "@popperjs/core": "2.11.8", "apexcharts": "3.49.2", "bootstrap": "5.3.3", "jquery": "^3.7.1", "ng-apexcharts": "1.11.0", "ng-zorro-antd": "^19.2.1", "ngx-scrollbar": "16.1.1", "rxjs": "~7.8.1", "screenfull": "6.0.2", "tslib": "2.8.1", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "19.0.6", "@angular/cli": "19.0.6", "@angular/compiler-cli": "19.0.5", "@eslint/eslintrc": "3.2.0", "@eslint/js": "9.17.0", "@types/jasmine": "~5.1.5", "@types/lodash": "4.17.13", "@types/node": "22.10.2", "angular-eslint": "19.0.2", "eslint": "^9.16.0", "jasmine-core": "~5.5.0", "karma": "~6.4.4", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.1", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "prettier": "3.4.2", "sass": "^1.86.3", "typescript": "5.6.3", "typescript-eslint": "8.18.0"}}