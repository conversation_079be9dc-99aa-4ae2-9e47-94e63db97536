// Angular import
import { Component, OnInit, inject } from '@angular/core';
import { NavigationCancel, NavigationEnd, NavigationError, NavigationStart, Router, RouterModule } from '@angular/router';
import { InactivityService } from './../app/theme/shared/inactivity.service';
// project import
import { SpinnerComponent } from './theme/shared/components/spinner/spinner.component';
import { Subscription } from 'rxjs';
@Component({
  selector: 'app-root',
  imports: [SpinnerComponent, RouterModule],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  private router = inject(Router);
  isSpinnerVisible = true;
  title = 'datta-able';
constructor(private routers: Router,private inactivityService: InactivityService) {
 this.router.events.subscribe({
  next: (event) => {
    if (event instanceof NavigationStart) {
      this.isSpinnerVisible = true;
    } else if (event instanceof NavigationEnd || event instanceof NavigationCancel || event instanceof NavigationError) {
      this.isSpinnerVisible = false;
    }
  },
  error: (error) => {
    console.error('Router event error:', error); // Optional: log the error
    this.isSpinnerVisible = false;
  },
  complete: () => {
    // This complete callback is less common for router events,
    // but included for completeness as per the observer pattern.
    // Router events typically don't "complete" unless the router is destroyed.
    this.isSpinnerVisible = false;
  }
});
}
  // life cycle hook
  // ngOnInit() {
  //   this.router.events.subscribe((evt) => {
  //     if (!(evt instanceof NavigationEnd)) {
  //       return;
  //     }
  //     window.scrollTo(0, 0);
  //   });
  // }

  ngOnInit(): void {
    this.inactivityService.startWatching();

  }

  ngOnDestroy(): void {
    // Stop watching when the component is destroyed
    this.inactivityService.stopWatching();
  }
}
