<div class="dashboard-container">
  <div class="row">
    <!-- Loading Indicator -->
    @if (loading) {
      <div class="col-12 text-center p-5 loading-container">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-3">Loading dashboard data...</p>
      </div>
    } @else {
      <!-- Dashboard Header -->
      <div class="col-12 mb-4">
        <h2 class="dashboard-title">Dashboard Overview</h2>
        <p class="dashboard-subtitle">Monitor your devices and content performance</p>
      </div>

      <!-- Device Status Cards -->
      <div class="col-md-4 col-xl-4" *ngFor="let item of [
        {title: 'Total Devices', value: totalDevices, icon: 'icon-monitor', class: 'bg-gradient-primary'},
        {title: 'Active Devices', value: activeDevices, icon: 'icon-check-circle', class: 'bg-gradient-success'},
        {title: 'Offline Devices', value: offlineDevices, icon: 'icon-x-circle', class: 'bg-gradient-danger'}
      ]; let i = index" [style.animation-delay]="i * 0.1 + 's'">
        <app-card [hidHeader]="true" class="dashboard-card">
          <div class="dashboard-info-card {{item.class}}">
            <div class="icon-box">
              <i class="feather {{item.icon}} text-white"></i>
            </div>
            <div class="content">
              <h2 class="text-white mb-2">{{ item.value }}</h2>
              <p class="text-white mb-0">{{ item.title }}</p>
            </div>
          </div>
        </app-card>
      </div>

      <!-- Content Status Cards -->
      <div class="col-md-4 col-xl-4" *ngFor="let item of [
        {title: 'Total Content', value: totalContent, icon: 'icon-file', class: 'bg-gradient-info'},
        {title: 'Online Content', value: onlineContent, icon: 'icon-check', class: 'bg-gradient-success'},
        {title: 'Offline Content', value: offlineContent, icon: 'icon-alert-circle', class: 'bg-gradient-warning'}
      ]; let i = index" [style.animation-delay]="(i+3) * 0.1 + 's'">
        <app-card [hidHeader]="true" class="dashboard-card">
          <div class="dashboard-info-card {{item.class}}">
            <div class="icon-box">
              <i class="feather {{item.icon}} text-white"></i>
            </div>
            <div class="content">
              <h2 class="text-white mb-2">{{ item.value }}</h2>
              <p class="text-white mb-0">{{ item.title }}</p>
            </div>
          </div>
        </app-card>
      </div>

      <!-- Charts Section Header -->
      <div class="col-12 mt-4 mb-3">
        <h3 class="charts-section-title">Analytics & Statistics</h3>
      </div>

      <!-- Device Status Chart -->
      <div class="col-md-6 col-xl-6">
        <app-card cardTitle="Device Status Distribution" [options]="false" class="chart-card">
          @if (deviceStatusLoading) {
            <div class="text-center p-4">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
          } @else {
            <div class="chart-container">
              <apx-chart
                #deviceStatusChart
                [series]="deviceStatusChartOptions.series"
                [chart]="deviceStatusChartOptions.chart"
                [labels]="deviceStatusChartOptions.labels"
                [colors]="deviceStatusChartOptions.colors"
                [legend]="deviceStatusChartOptions.legend"
                [dataLabels]="deviceStatusChartOptions.dataLabels"
                [tooltip]="deviceStatusChartOptions.tooltip"
              ></apx-chart>
            </div>
          }
        </app-card>
      </div>

      <!-- Content Status Chart -->
      <div class="col-md-6 col-xl-6">
        <app-card cardTitle="Content Status Distribution" [options]="false" class="chart-card">
          @if (contentStatusLoading) {
            <div class="text-center p-4">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
          } @else {
            <div class="chart-container">
              <apx-chart
                #contentStatusChart
                [series]="contentStatusChartOptions.series"
                [chart]="contentStatusChartOptions.chart"
                [labels]="contentStatusChartOptions.labels"
                [colors]="contentStatusChartOptions.colors"
                [legend]="contentStatusChartOptions.legend"
                [dataLabels]="contentStatusChartOptions.dataLabels"
                [tooltip]="contentStatusChartOptions.tooltip"
              ></apx-chart>
            </div>
          }
        </app-card>
      </div>

      <!-- Content Per Device Chart -->
      <div class="col-md-12 col-xl-12">
        <app-card cardTitle="Content Per Device" [options]="false" class="chart-card">
          @if (contentPerDeviceLoading) {
            <div class="text-center p-4">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
          } @else {
            <div class="chart-container">
              <apx-chart
                #contentPerDeviceChart
                [series]="contentPerDeviceChartOptions.series"
                [chart]="contentPerDeviceChartOptions.chart"
                [dataLabels]="contentPerDeviceChartOptions.dataLabels"
                [plotOptions]="contentPerDeviceChartOptions.plotOptions"
                [yaxis]="contentPerDeviceChartOptions.yaxis"
                [legend]="contentPerDeviceChartOptions.legend"
                [fill]="contentPerDeviceChartOptions.fill"
                [stroke]="contentPerDeviceChartOptions.stroke"
                [tooltip]="contentPerDeviceChartOptions.tooltip"
                [xaxis]="contentPerDeviceChartOptions.xaxis"
                [colors]="contentPerDeviceChartOptions.colors"
              ></apx-chart>
            </div>
          }
        </app-card>
      </div>

      <!-- Content Type Chart -->
      <div class="col-md-12 col-xl-12">
        <app-card cardTitle="Content Types Per Device" [options]="false" class="chart-card">
          @if (contentTypeLoading) {
            <div class="text-center p-4">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>
          } @else {
            <div class="chart-container">
              <apx-chart
                #contentTypeChart
                [series]="contentTypeChartOptions.series"
                [chart]="contentTypeChartOptions.chart"
                [dataLabels]="contentTypeChartOptions.dataLabels"
                [plotOptions]="contentTypeChartOptions.plotOptions"
                [yaxis]="contentTypeChartOptions.yaxis"
                [legend]="contentTypeChartOptions.legend"
                [fill]="contentTypeChartOptions.fill"
                [stroke]="contentTypeChartOptions.stroke"
                [tooltip]="contentTypeChartOptions.tooltip"
                [xaxis]="contentTypeChartOptions.xaxis"
                [colors]="contentTypeChartOptions.colors"
              ></apx-chart>
            </div>
          }
        </app-card>
      </div>

      <!-- Quick Actions Section -->
     
    }
  </div>
</div>
