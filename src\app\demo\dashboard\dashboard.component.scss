// Modern Dashboard Styling

// Dashboard Card Styling
.dashboard-card {
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  margin-bottom: 28px;
  transform: translateZ(0); // Hardware acceleration
  will-change: transform, box-shadow; // Optimize animations

  &:hover {
    transform: translateY(-8px) scale(1.01);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
  }

  // Match with sidebar
  ::ng-deep .card {
    border: none;
    box-shadow: none;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);

    .card-header {
      background-color: transparent;
      border-bottom: 1px solid rgba(61, 20, 71, 0.08);
      font-weight: 600;
      padding: 18px 24px;
      letter-spacing: 0.3px;
    }
  }
}

// Info Card Styling
.dashboard-info-card {
  display: flex;
  align-items: center;
  padding: 28px;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;

  // Modern glass morphism effect
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 10% 10%, rgba(255, 255, 255, 0.2) 0%, transparent 60%),
      radial-gradient(circle at 90% 90%, rgba(255, 255, 255, 0.15) 0%, transparent 60%),
      linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 80%);
    opacity: 0.9;
    z-index: 0;
    transition: all 0.5s ease;
  }

  &:hover::before {
    opacity: 1;
    background-image:
      radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.25) 0%, transparent 70%),
      radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.2) 0%, transparent 70%),
      linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, transparent 80%);
  }

  .icon-box {
    width: 68px;
    height: 68px;
    border-radius: 16px;
    background: rgba(255, 255, 255, 0.25);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 24px;
    position: relative;
    z-index: 1;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    // Completely remove any black box styling
    &::before, &::after {
      display: none !important;
      content: none !important;
      background: none !important;
    }

    i, span, * {
      background: transparent !important;
      box-shadow: none !important;
      border: none !important;
      background-color: transparent !important;
      background-image: none !important;
      font-size: 28px;
      transition: all 0.3s ease;
    }
  }

  &:hover .icon-box {
    transform: scale(1.05) rotate(5deg);
    background: rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  &:hover .icon-box i {
    transform: scale(1.1);
  }

  .content {
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;

    h2 {
      font-size: 32px;
      font-weight: 700;
      margin-bottom: 8px;
      letter-spacing: 0.5px;
      transition: all 0.3s ease;
    }

    p {
      font-size: 15px;
      opacity: 0.95;
      font-weight: 500;
      letter-spacing: 0.3px;
      transition: all 0.3s ease;
    }
  }

  &:hover .content h2 {
    transform: translateY(-2px);
    text-shadow: 0 2px 10px rgba(255, 255, 255, 0.3);
  }

  &:hover .content p {
    transform: translateY(2px);
  }
}

// Gradient Backgrounds with modern gradients
.bg-gradient-primary {
  background: linear-gradient(135deg, #3d1447 0%, #7a3b8f 100%);
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(122, 59, 143, 0.4) 0%, rgba(61, 20, 71, 0) 100%);
    z-index: 0;
    opacity: 0;
    transition: opacity 0.5s ease;
  }

  &:hover::after {
    opacity: 1;
  }
}

.bg-gradient-success {
  background: linear-gradient(135deg, #0cebad 0%, #0cb8eb 100%);
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(12, 235, 173, 0.4) 0%, rgba(12, 184, 235, 0) 100%);
    z-index: 0;
    opacity: 0;
    transition: opacity 0.5s ease;
  }

  &:hover::after {
    opacity: 1;
  }
}

.bg-gradient-danger {
  background: linear-gradient(135deg, #ff4d4d 0%, #ff7676 100%);
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 77, 77, 0.4) 0%, rgba(255, 118, 118, 0) 100%);
    z-index: 0;
    opacity: 0;
    transition: opacity 0.5s ease;
  }

  &:hover::after {
    opacity: 1;
  }
}

.bg-gradient-warning {
  background: linear-gradient(135deg, #ffba08 0%, #ffd166 100%);
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 186, 8, 0.4) 0%, rgba(255, 209, 102, 0) 100%);
    z-index: 0;
    opacity: 0;
    transition: opacity 0.5s ease;
  }

  &:hover::after {
    opacity: 1;
  }
}

.bg-gradient-info {
  background: linear-gradient(135deg, #3ac7ff 0%, #0080ff 100%);
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(58, 199, 255, 0.4) 0%, rgba(0, 128, 255, 0) 100%);
    z-index: 0;
    opacity: 0;
    transition: opacity 0.5s ease;
  }

  &:hover::after {
    opacity: 1;
  }
}

// Chart Card Styling
apx-chart {
  display: block;
  min-height: 350px;
  transition: all 0.3s ease;
}

// Dashboard container styling
.dashboard-container {
  padding: 20px 15px;
  animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

// Dashboard header styling
.dashboard-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 8px;
  color: #333;
  letter-spacing: 0.5px;
}

.dashboard-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
}

.charts-section-title {
  font-size: 22px;
  font-weight: 600;
  color: #444;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

// Quick actions styling
.quick-actions-card {
  margin-bottom: 30px;

  ::ng-deep .card-header {
    border-bottom-color: rgba(0, 0, 0, 0.05);
  }
}

.quick-action-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
  animation: fadeIn 0.5s ease-in-out;
  animation-fill-mode: both;

  i {
    font-size: 24px;
    margin-bottom: 15px;
    color: #3d1447;
    transition: all 0.3s ease;
  }

  h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
  }

  p {
    font-size: 13px;
    color: #666;
    margin-bottom: 15px;
  }

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);

    i {
      transform: scale(1.2);
    }
  }

  &.action-primary i { color: #3d1447; }
  &.action-success i { color: #0cebad; }
  &.action-info i { color: #3ac7ff; }
  &.action-warning i { color: #ffba08; }
}

// Chart card styling
.chart-card {
  transition: all 0.4s ease;
  border-radius: 16px;
  overflow: hidden;
  animation: fadeIn 0.8s ease-in-out;
  animation-fill-mode: both;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  }

  ::ng-deep .card-header {
    padding: 20px 24px;
    font-weight: 600;
    letter-spacing: 0.3px;
    font-size: 18px;
  }
}

// Chart container styling
.chart-container {
  position: relative;
  padding: 10px;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
    opacity: 0;
    transform: scale(0.5);
    transition: all 0.8s ease;
    z-index: 0;
    pointer-events: none;
  }

  &:hover::before {
    opacity: 1;
    transform: scale(1);
  }
}

// Global styles to fix black boxes and match sidebar theme
::ng-deep {
  // Fix for black boxes in any icons
  .feather, i[class*="icon-"], i[class*="feather"] {
    background: transparent !important;
    box-shadow: none !important;
    border: none !important;
    background-color: transparent !important;
    background-image: none !important;

    &::before, &::after {
      background: transparent !important;
      box-shadow: none !important;
      border: none !important;
    }
  }

  // Chart styling
  .apexcharts-legend-text {
    color: #333 !important;
    font-weight: 500 !important;
    font-size: 13px !important;
  }

  .apexcharts-tooltip {
    background: rgba(61, 20, 71, 0.9) !important;
    color: #fff !important;
    border: none !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25) !important;
    border-radius: 8px !important;
    backdrop-filter: blur(6px) !important;
    padding: 8px 12px !important;
    font-weight: 500 !important;
  }

  .apexcharts-tooltip-title {
    background: rgba(92, 42, 106, 0.9) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15) !important;
    padding: 8px 12px !important;
    font-weight: 600 !important;
  }

  .apexcharts-xaxistooltip {
    background: rgba(61, 20, 71, 0.9) !important;
    color: #fff !important;
    border: none !important;
    border-radius: 6px !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;

    &:after, &:before {
      border-bottom-color: rgba(61, 20, 71, 0.9) !important;
    }
  }

  // Chart animations
  .apexcharts-canvas {
    .apexcharts-series {
      transition: all 0.3s ease !important;
    }

    .apexcharts-bar-series, .apexcharts-area-series, .apexcharts-line-series {
      &:hover {
        opacity: 0.9;
      }
    }
  }

  // Improve chart interactions
  .apexcharts-zoom-icon, .apexcharts-reset-icon, .apexcharts-menu-icon {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 6px;

    &:hover {
      background: #f0f0f0 !important;
    }
  }
}

// Modern Loading Spinner
.spinner-border {
  width: 3.5rem;
  height: 3.5rem;
  border-width: 0.25rem;
  animation: spinner-border 1.2s linear infinite;
  box-shadow: 0 0 20px rgba(61, 20, 71, 0.2);
}

.loading-container {
  animation: fadeIn 0.5s ease-in-out;

  p {
    font-size: 16px;
    font-weight: 500;
    color: #666;
    margin-top: 15px;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .dashboard-info-card {
    padding: 20px;

    .icon-box {
      width: 55px;
      height: 55px;
      margin-right: 18px;
      border-radius: 12px;
    }

    .content {
      h2 {
        font-size: 24px;
      }

      p {
        font-size: 13px;
      }
    }
  }

  .chart-card {
    margin-bottom: 20px;
  }
}
