// angular import
import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';

// project import
import { SharedModule } from 'src/app/theme/shared/shared.module';
import { DashboardService, DeviceContent, DeviceContentType } from './dashboard.service';

// third party
import { ApexOptions, ChartComponent, NgApexchartsModule } from 'ng-apexcharts';

declare const AmCharts;

import '../../../assets/charts/amchart/amcharts.js';
import '../../../assets/charts/amchart/gauge.js';
import '../../../assets/charts/amchart/serial.js';
import '../../../assets/charts/amchart/light.js';
import '../../../assets/charts/amchart/pie.min.js';
import '../../../assets/charts/amchart/ammap.min.js';
import '../../../assets/charts/amchart/usaLow.js';
import '../../../assets/charts/amchart/radar.js';
import '../../../assets/charts/amchart/worldLow.js';

import dataJson from 'src/fake-data/map_data';
import mapColor from 'src/fake-data/map-color-data.json';

@Component({
  selector: 'app-dashboard',
  imports: [CommonModule, SharedModule, NgApexchartsModule],
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {
  @ViewChild('deviceStatusChart') deviceStatusChart: ChartComponent;
  @ViewChild('contentStatusChart') contentStatusChart: ChartComponent;
  @ViewChild('contentPerDeviceChart') contentPerDeviceChart: ChartComponent;
  @ViewChild('contentTypeChart') contentTypeChart: ChartComponent;

  // Dashboard data
  totalDevices: number = 0;
  activeDevices: number = 0;
  offlineDevices: number = 0;
  totalContent: number = 0;
  onlineContent: number = 0;
  offlineContent: number = 0;
  deviceContentData: DeviceContent[] = [];
  deviceContentTypeData: DeviceContentType[] = [];

  // Quick actions for dashboard
  quickActions = [
    {
      title: 'Add Device',
      description: 'Register a new device in the system',
      icon: 'icon-plus-circle',
      buttonText: 'Add New',
      class: 'action-primary'
    },
    {
      title: 'Upload Content',
      description: 'Add new content to your devices',
      icon: 'icon-upload-cloud',
      buttonText: 'Upload',
      class: 'action-success'
    },
    {
      title: 'Device Status',
      description: 'Check the status of all devices',
      icon: 'icon-activity',
      buttonText: 'View Status',
      class: 'action-info'
    },
    {
      title: 'Content Schedule',
      description: 'Manage your content schedule',
      icon: 'icon-calendar',
      buttonText: 'Schedule',
      class: 'action-warning'
    }
  ];

  // Loading states
  loading: boolean = true;
  deviceStatusLoading: boolean = true;
  contentStatusLoading: boolean = true;
  contentPerDeviceLoading: boolean = true;
  contentTypeLoading: boolean = true;

  // Chart options
  deviceStatusChartOptions: Partial<ApexOptions>;
  contentStatusChartOptions: Partial<ApexOptions>;
  contentPerDeviceChartOptions: Partial<ApexOptions>;
  contentTypeChartOptions: Partial<ApexOptions>;

  constructor(private dashboardService: DashboardService) {
    // Initialize chart options with empty data
    this.initChartOptions();
  }

  // life cycle event
  ngOnInit() {
    // Load dashboard data
    this.loadDashboardData();

    // Initialize AmCharts (keeping this for backward compatibility)
    setTimeout(() => {
      const latlong = dataJson;
      const mapData = mapColor;

      const minBulletSize = 3;
      const maxBulletSize = 70;
      let min = Infinity;
      let max = -Infinity;
      let i;
      let value;
      for (i = 0; i < mapData.length; i++) {
        value = mapData[i].value;
        if (value < min) {
          min = value;
        }
        if (value > max) {
          max = value;
        }
      }

      const maxSquare = maxBulletSize * maxBulletSize * 2 * Math.PI;
      const minSquare = minBulletSize * minBulletSize * 2 * Math.PI;

      const images = [];
      for (i = 0; i < mapData.length; i++) {
        const dataItem = mapData[i];
        value = dataItem.value;

        let square = ((value - min) / (max - min)) * (maxSquare - minSquare) + minSquare;
        if (square < minSquare) {
          square = minSquare;
        }
        const size = Math.sqrt(square / (Math.PI * 8));
        const id = dataItem.code;

        images.push({
          type: 'circle',
          theme: 'light',
          width: size,
          height: size,
          color: dataItem.color,
          longitude: latlong[id].longitude,
          latitude: latlong[id].latitude,
          title: dataItem.name + '</br> [ ' + value + ' ]',
          value: value
        });
      }

      // world-low chart
      AmCharts.makeChart('world-low', {
        type: 'map',
        projection: 'eckert6',
        dataProvider: {
          map: 'worldLow',
          images: images
        },
        export: {
          enabled: true
        }
      });
    }, 500);
  }

  /**
   * Initialize chart options with default values
   */
  private initChartOptions(): void {
    // Common chart theme settings
    const chartTheme = {
      mode: 'light' as 'light',
      monochrome: {
        enabled: false
      }
    };

    // Common animation settings
    const animations = {
      enabled: true,
      easing: 'easeinout' as 'easeinout',
      speed: 800,
      animateGradually: {
        enabled: true,
        delay: 150
      },
      dynamicAnimation: {
        enabled: true,
        speed: 350
      }
    };

    // Device Status Chart Options
    this.deviceStatusChartOptions = {
      series: [0, 0],
      chart: {
        type: 'donut',
        height: 320,
        fontFamily: 'inherit',
        background: 'transparent',
        animations: animations,
        toolbar: {
          show: true,
          tools: {
            download: true,
            selection: true,
            zoom: true,
            zoomin: true,
            zoomout: true,
            pan: true,
            reset: true
          }
        },
        dropShadow: {
          enabled: true,
          top: 5,
          left: 0,
          blur: 5,
          opacity: 0.2
        }
      },
      theme: chartTheme,
      labels: ['Active Devices', 'Offline Devices'],
      colors: ['#0cebad', '#ff4d4d'],
      legend: {
        position: 'bottom',
        horizontalAlign: 'center',
        fontWeight: 500,
        fontSize: '14px',
        markers: {
          width: 12,
          height: 12,
          strokeWidth: 0,
          radius: 12
        },
        itemMargin: {
          horizontal: 10,
          vertical: 5
        }
      },
      plotOptions: {
        pie: {
          donut: {
            size: '60%',
            labels: {
              show: true,
              name: {
                show: true,
                fontSize: '16px',
                fontWeight: 600,
                offsetY: -10
              },
              value: {
                show: true,
                fontSize: '20px',
                fontWeight: 700,
                formatter: function(val) {
                  return val;
                }
              },
              total: {
                show: true,
                label: 'Total',
                fontSize: '16px',
                fontWeight: 600,
                formatter: function(w) {
                  return w.globals.seriesTotals.reduce((a, b) => a + b, 0);
                }
              }
            }
          }
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function(val) {
          return typeof val === 'number' ? val.toFixed(1) + "%" : val + "%";
        },
        style: {
          fontSize: '14px',
          fontWeight: 600
        },
        dropShadow: {
          enabled: true
        }
      },
      tooltip: {
        enabled: true,
        theme: 'dark' as 'dark',
        style: {
          fontSize: '14px'
        },
        y: {
          formatter: function(val) {
            return val + " devices";
          }
        }
      },
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: {
              height: 280
            },
            legend: {
              position: 'bottom'
            }
          }
        }
      ]
    };

    // Content Status Chart Options
    this.contentStatusChartOptions = {
      series: [0, 0],
      chart: {
        type: 'donut',
        height: 320,
        fontFamily: 'inherit',
        background: 'transparent',
        animations: animations,
        toolbar: {
          show: true
        },
        dropShadow: {
          enabled: true,
          top: 5,
          left: 0,
          blur: 5,
          opacity: 0.2
        }
      },
      theme: chartTheme,
      labels: ['Online Content', 'Offline Content'],
      colors: ['#3d1447', '#ffba08'],
      legend: {
        position: 'bottom',
        horizontalAlign: 'center',
        fontWeight: 500,
        fontSize: '14px',
        markers: {
          width: 12,
          height: 12,
          strokeWidth: 0,
          radius: 12
        },
        itemMargin: {
          horizontal: 10,
          vertical: 5
        }
      },
      plotOptions: {
        pie: {
          donut: {
            size: '60%',
            labels: {
              show: true,
              name: {
                show: true,
                fontSize: '16px',
                fontWeight: 600,
                offsetY: -10
              },
              value: {
                show: true,
                fontSize: '20px',
                fontWeight: 700,
                formatter: function(val) {
                  return val;
                }
              },
              total: {
                show: true,
                label: 'Total',
                fontSize: '16px',
                fontWeight: 600,
                formatter: function(w) {
                  return w.globals.seriesTotals.reduce((a, b) => a + b, 0);
                }
              }
            }
          }
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function(val) {
          return typeof val === 'number' ? val.toFixed(1) + "%" : val + "%";
        },
        style: {
          fontSize: '14px',
          fontWeight: 600
        },
        dropShadow: {
          enabled: true
        }
      },
      tooltip: {
        enabled: true,
        theme: 'dark' as 'dark',
        style: {
          fontSize: '14px'
        },
        y: {
          formatter: function(val) {
            return val + " items";
          }
        }
      },
      responsive: [
        {
          breakpoint: 480,
          options: {
            chart: {
              height: 280
            },
            legend: {
              position: 'bottom'
            }
          }
        }
      ]
    };

    // Content Per Device Chart Options
    this.contentPerDeviceChartOptions = {
      series: [{
        name: 'Online Content',
        data: []
      }],
      chart: {
        type: 'bar',
        height: 380,
        fontFamily: 'inherit',
        background: 'transparent',
        animations: animations,
        toolbar: {
          show: true
        },
        dropShadow: {
          enabled: true,
          top: 5,
          left: 0,
          blur: 5,
          opacity: 0.1
        }
      },
      theme: chartTheme,
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '60%',
          borderRadius: 8,
          dataLabels: {
            position: 'top'
          }
        }
      },
      dataLabels: {
        enabled: true,
        offsetY: -20,
        style: {
          fontSize: '12px',
          colors: ["#304758"]
        }
      },
      stroke: {
        show: true,
        width: 2,
        colors: ['transparent']
      },
      xaxis: {
        categories: [],
        labels: {
          style: {
            fontSize: '13px',
            fontWeight: 500
          }
        },
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        }
      },
      yaxis: {
        title: {
          text: 'Content Count',
          style: {
            fontSize: '14px',
            fontWeight: 600
          }
        },
        labels: {
          style: {
            fontSize: '13px'
          }
        }
      },
      fill: {
        opacity: 1,
        type: 'gradient',
        gradient: {
          shade: 'dark',
          type: 'vertical',
          shadeIntensity: 0.3,
          gradientToColors: undefined,
          inverseColors: false,
          opacityFrom: 1,
          opacityTo: 0.8,
          stops: [0, 100]
        }
      },
      tooltip: {
        enabled: true,
        theme: 'dark' as 'dark',
        style: {
          fontSize: '14px'
        },
        y: {
          formatter: function(val) {
            return val + " items";
          }
        }
      },
      colors: ['#3ac7ff'],
      grid: {
        borderColor: '#f1f1f1',
        strokeDashArray: 4,
        xaxis: {
          lines: {
            show: false
          }
        },
        yaxis: {
          lines: {
            show: true
          }
        }
      },
      legend: {
        show: true,
        position: 'bottom',
        horizontalAlign: 'center',
        fontSize: '14px',
        markers: {
          radius: 12
        }
      }
    };

    // Content Type Chart Options
    this.contentTypeChartOptions = {
      series: [{
        name: 'Video Content',
        data: []
      }, {
        name: 'Image Content',
        data: []
      }],
      chart: {
        type: 'bar',
        height: 380,
        fontFamily: 'inherit',
        background: 'transparent',
        stacked: true,
        animations: animations,
        toolbar: {
          show: true
        },
        dropShadow: {
          enabled: true,
          top: 5,
          left: 0,
          blur: 5,
          opacity: 0.1
        }
      },
      theme: chartTheme,
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '60%',
          borderRadius: 8,
          dataLabels: {
            total: {
              enabled: true,
              style: {
                fontSize: '13px',
                fontWeight: 600
              }
            }
          }
        }
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        show: true,
        width: 2,
        colors: ['transparent']
      },
      xaxis: {
        categories: [],
        labels: {
          style: {
            fontSize: '13px',
            fontWeight: 500
          }
        },
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        }
      },
      yaxis: {
        title: {
          text: 'Content Count',
          style: {
            fontSize: '14px',
            fontWeight: 600
          }
        },
        labels: {
          style: {
            fontSize: '13px'
          }
        }
      },
      fill: {
        opacity: 1,
        type: 'gradient',
        gradient: {
          shade: 'dark',
          type: 'vertical',
          shadeIntensity: 0.3,
          gradientToColors: undefined,
          inverseColors: false,
          opacityFrom: 1,
          opacityTo: 0.8,
          stops: [0, 100]
        }
      },
      tooltip: {
        enabled: true,
        theme: 'dark' as 'dark',
        style: {
          fontSize: '14px'
        },
        y: {
          formatter: function(val) {
            return val + " items";
          }
        }
      },
      colors: ['#7a3b8f', '#3ac7ff'],
      grid: {
        borderColor: '#f1f1f1',
        strokeDashArray: 4,
        xaxis: {
          lines: {
            show: false
          }
        },
        yaxis: {
          lines: {
            show: true
          }
        }
      },
      legend: {
        show: true,
        position: 'bottom',
        horizontalAlign: 'center',
        fontSize: '14px',
        markers: {
          radius: 12
        }
      }
    };
  }

  /**
   * Load all dashboard data from API
   */
  private loadDashboardData(): void {
    this.loading = true;

    // Get total devices count
    this.dashboardService.getTotalDevicesCount().subscribe({
      next: (data) => {
        this.totalDevices = data.totalDevices;
      },
      error: (error) => {
        console.error('Error fetching total devices count:', error);
      }
    });

    // Get device status counts
    this.deviceStatusLoading = true;
    this.dashboardService.getDeviceStatusCounts().subscribe({
      next: (data) => {
        this.activeDevices = data.activeDevices;
        this.offlineDevices = data.offlineDevices;

        // Update device status chart
        this.deviceStatusChartOptions.series = [this.activeDevices, this.offlineDevices];
        this.deviceStatusLoading = false;
      },
      error: (error) => {
        console.error('Error fetching device status counts:', error);
        this.deviceStatusLoading = false;
      }
    });

    // Get content status counts
    this.contentStatusLoading = true;
    this.dashboardService.getContentStatusCounts().subscribe({
      next: (data) => {
        this.onlineContent = data.onlineContent;
        this.offlineContent = data.offlineContent;
        this.totalContent = data.totalContent;

        // Update content status chart
        this.contentStatusChartOptions.series = [this.onlineContent, this.offlineContent];
        this.contentStatusLoading = false;
      },
      error: (error) => {
        console.error('Error fetching content status counts:', error);
        this.contentStatusLoading = false;
      }
    });

    // Get content per device
    this.contentPerDeviceLoading = true;
    this.dashboardService.getContentPerDevice().subscribe({
      next: (data) => {
        this.deviceContentData = data;

        // Update content per device chart
        const categories = data.map(item => item.name);
        const seriesData = data.map(item => item.onlineContent);

        this.contentPerDeviceChartOptions.xaxis = {
          categories: categories
        };
        this.contentPerDeviceChartOptions.series = [{
          name: 'Online Content',
          data: seriesData
        }];

        this.contentPerDeviceLoading = false;
      },
      error: (error) => {
        console.error('Error fetching content per device:', error);
        this.contentPerDeviceLoading = false;
      }
    });

    // Get content type per device
    this.contentTypeLoading = true;
    this.dashboardService.getContentTypePerDevice().subscribe({
      next: (data) => {
        this.deviceContentTypeData = data;

        // Update content type chart
        const categories = data.map(item => item.name);
        const videoData = data.map(item => item.videoContent);
        const imageData = data.map(item => item.imageContent);

        this.contentTypeChartOptions.xaxis = {
          categories: categories
        };
        this.contentTypeChartOptions.series = [
          {
            name: 'Video Content',
            data: videoData
          },
          {
            name: 'Image Content',
            data: imageData
          }
        ];

        this.contentTypeLoading = false;
      },
      error: (error) => {
        console.error('Error fetching content type per device:', error);
        this.contentTypeLoading = false;
      },
      complete: () => {
        this.loading = false;
      }
    });
  }

  // public method
  sales = [
    {
      title: 'Daily Sales',
      icon: 'icon-arrow-up text-c-green',
      amount: '$249.95',
      percentage: '67%',
      progress: 50,
      design: 'col-md-6',
      progress_bg: 'progress-c-theme'
    },
    {
      title: 'Monthly Sales',
      icon: 'icon-arrow-down text-c-red',
      amount: '$2,942.32',
      percentage: '36%',
      progress: 35,
      design: 'col-md-6',
      progress_bg: 'progress-c-theme2'
    },
    {
      title: 'Yearly Sales',
      icon: 'icon-arrow-up text-c-green',
      amount: '$8,638.32',
      percentage: '80%',
      progress: 70,
      design: 'col-md-12',
      progress_bg: 'progress-c-theme'
    }
  ];

  card = [
    {
      design: 'border-bottom',
      number: '235',
      text: 'TOTAL IDEAS',
      icon: 'icon-zap text-c-green'
    },
    {
      number: '26',
      text: 'TOTAL LOCATIONS',
      icon: 'icon-map-pin text-c-blue'
    }
  ];

  social_card = [
    {
      design: 'col-md-12',
      icon: 'fab fa-facebook-f text-primary',
      amount: '12,281',
      percentage: '+7.2%',
      color: 'text-c-green',
      target: '35,098',
      progress: 60,
      duration: '3,539',
      progress2: 45,
      progress_bg: 'progress-c-theme',
      progress_bg_2: 'progress-c-theme2'
    },
    {
      design: 'col-md-6',
      icon: 'fab fa-twitter text-c-blue',
      amount: '11,200',
      percentage: '+6.2%',
      color: 'text-c-purple',
      target: '34,185',
      progress: 40,
      duration: '4,567',
      progress2: 70,
      progress_bg: 'progress-c-theme',
      progress_bg_2: 'progress-c-theme2'
    },
    {
      design: 'col-md-6',
      icon: 'fab fa-google-plus-g text-c-red',
      amount: '10,500',
      percentage: '+5.9%',
      color: 'text-c-blue',
      target: '25,998',
      progress: 80,
      duration: '7,753',
      progress2: 50,
      progress_bg: 'progress-c-theme',
      progress_bg_2: 'progress-c-theme2'
    }
  ];

  progressing = [
    {
      number: '5',
      amount: '384',
      progress: 70,
      progress_bg: 'progress-c-theme'
    },
    {
      number: '4',
      amount: '145',
      progress: 35,
      progress_bg: 'progress-c-theme'
    },
    {
      number: '3',
      amount: '24',
      progress: 25,
      progress_bg: 'progress-c-theme'
    },
    {
      number: '2',
      amount: '1',
      progress: 10,
      progress_bg: 'progress-c-theme'
    },
    {
      number: '1',
      amount: '0',
      progress: 0,
      progress_bg: 'progress-c-theme'
    }
  ];

  tables = [
    {
      src: 'assets/images/user/avatar-1.jpg',
      title: 'Isabella Christensen',
      text: 'Requested account activation',
      time: '11 MAY 12:56',
      color: 'text-c-green'
    },
    {
      src: 'assets/images/user/avatar-2.jpg',
      title: 'Ida Jorgensen',
      text: 'Pending document verification',
      time: '11 MAY 10:35',
      color: 'text-c-red'
    },
    {
      src: 'assets/images/user/avatar-3.jpg',
      title: 'Mathilda Andersen',
      text: 'Completed profile setup',
      time: '9 MAY 17:38',
      color: 'text-c-green'
    },
    {
      src: 'assets/images/user/avatar-1.jpg',
      title: 'Karla Soreness',
      text: 'Requires additional information',
      time: '19 MAY 12:56',
      color: 'text-c-red'
    },
    {
      src: 'assets/images/user/avatar-2.jpg',
      title: 'Albert Andersen',
      text: 'Approved and verified account',
      time: '21 July 12:56',
      color: 'text-c-green'
    }
  ];
}
