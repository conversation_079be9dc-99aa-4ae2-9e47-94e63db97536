import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { StorageService } from 'src/app/theme/shared/service/storage.service';

// Interface for API responses
interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

// Interfaces for dashboard data
export interface TotalDevicesCount {
  totalDevices: number;
}

export interface DeviceStatusCounts {
  activeDevices: number;
  offlineDevices: number;
  totalDevices: number;
}

export interface ContentStatusCounts {
  onlineContent: number;
  offlineContent: number;
  totalContent: number;
}

export interface DeviceContent {
  id: number;
  name: string;
  deviceId: string;
  deviceStatus: number;
  totalContent: number;
  onlineContent: number;
}

export interface DeviceContentType {
  id: number;
  name: string;
  deviceId: string;
  deviceStatus: number;
  videoContent: number;
  imageContent: number;
  totalOnlineContent: number;
}

@Injectable({
  providedIn: 'root'
})
export class DashboardService {
  private apiUrl = environment.URL;

  constructor(private http: HttpClient, private storageService: StorageService) { }

  private getHttpOptions() {
    const token = this.storageService.getToken();
    return {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      })
    };
  }

  /**
   * Get total devices count
   */
  getTotalDevicesCount(): Observable<TotalDevicesCount> {
    return this.http.get<ApiResponse<TotalDevicesCount>>(
      `${this.apiUrl}/dashboard/devices/total`, 
      this.getHttpOptions()
    ).pipe(
      map(response => response.data)
    );
  }

  /**
   * Get device status counts (active/offline)
   */
  getDeviceStatusCounts(): Observable<DeviceStatusCounts> {
    return this.http.get<ApiResponse<DeviceStatusCounts>>(
      `${this.apiUrl}/dashboard/devices/status`, 
      this.getHttpOptions()
    ).pipe(
      map(response => response.data)
    );
  }

  /**
   * Get content status counts (online/offline)
   */
  getContentStatusCounts(): Observable<ContentStatusCounts> {
    return this.http.get<ApiResponse<ContentStatusCounts>>(
      `${this.apiUrl}/dashboard/content/status`, 
      this.getHttpOptions()
    ).pipe(
      map(response => response.data)
    );
  }

  /**
   * Get content count per device
   */
  getContentPerDevice(): Observable<DeviceContent[]> {
    return this.http.get<ApiResponse<DeviceContent[]>>(
      `${this.apiUrl}/dashboard/content/per-device`, 
      this.getHttpOptions()
    ).pipe(
      map(response => response.data)
    );
  }

  /**
   * Get content type count per device
   */
  getContentTypePerDevice(): Observable<DeviceContentType[]> {
    return this.http.get<ApiResponse<DeviceContentType[]>>(
      `${this.apiUrl}/dashboard/content/types`, 
      this.getHttpOptions()
    ).pipe(
      map(response => response.data)
    );
  }
}
