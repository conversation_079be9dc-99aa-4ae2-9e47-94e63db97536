<div class="screen-control-container" [class.loading-container]="loadingStates.fetching">
  <div class="action-header">
    <h2 class="page-title">Ads & Events Management</h2>
    <div class="header-actions">
      <button (click)="showModal(undefined, 'ad')" class="add-button ad-btn">
        <span class="button-icon"><i class="feather icon-megaphone"></i></span>
        <span class="button-text">Create Ad</span>
      </button>
      <button (click)="showModal(undefined, 'event')" class="add-button event-btn">
        <span class="button-icon"><i class="feather icon-calendar"></i></span>
        <span class="button-text">Create Event</span>
      </button>
    </div>
  </div>

  <!-- Modern Custom Tabs -->
  <div class="ultra-modern-tabs">
    <div class="tabs-container">
      <div class="tab-item"
           [class.active]="activeTab === 0"
           [class.loading]="loadingStates.fetching && activeTab === 0"
           (click)="onTabChange(0)">
        <div class="tab-content">
          <div class="tab-icon">
            <nz-spin *ngIf="loadingStates.fetching && activeTab === 0" nzSize="small"></nz-spin>
            <i *ngIf="!loadingStates.fetching || activeTab !== 0" class="feather icon-megaphone"></i>
          </div>
          <div class="tab-text">
            <span class="tab-title">Advertisements</span>
            <span class="tab-subtitle">Manage your ads</span>
          </div>
        </div>
        <div class="tab-indicator"></div>
      </div>

      <div class="tab-item"
           [class.active]="activeTab === 1"
           [class.loading]="loadingStates.fetching && activeTab === 1"
           (click)="onTabChange(1)">
        <div class="tab-content">
          <div class="tab-icon">
            <nz-spin *ngIf="loadingStates.fetching && activeTab === 1" nzSize="small"></nz-spin>
            <i *ngIf="!loadingStates.fetching || activeTab !== 1" class="feather icon-calendar"></i>
          </div>
          <div class="tab-text">
            <span class="tab-title">Events</span>
            <span class="tab-subtitle">Manage your events</span>
          </div>
        </div>
        <div class="tab-indicator"></div>
      </div>

      <div class="tab-item"
           [class.active]="activeTab === 2"
           [class.loading]="loadingStates.fetching && activeTab === 2"
           (click)="onTabChange(2)">
        <div class="tab-content">
          <div class="tab-icon">
            <nz-spin *ngIf="loadingStates.fetching && activeTab === 2" nzSize="small"></nz-spin>
            <i *ngIf="!loadingStates.fetching || activeTab !== 2" class="feather icon-grid"></i>
          </div>
          <div class="tab-text">
            <span class="tab-title">All Items</span>
            <span class="tab-subtitle">View everything</span>
          </div>
        </div>
        <div class="tab-indicator"></div>
      </div>
    </div>
  </div>

  <!-- Loading Skeleton -->
  <div *ngIf="loadingStates.fetching" class="ads-grid mt-3">
    <div class="modern-ad-card skeleton-card" *ngFor="let i of [1,2,3,4,5,6,7,8,9]">
      <div class="skeleton-content">
        <nz-skeleton [nzActive]="true" [nzAvatar]="false" [nzParagraph]="{ rows: 3 }" [nzTitle]="true"></nz-skeleton>
        <div class="skeleton-image"></div>
        <div class="skeleton-actions">
          <div class="skeleton-button"></div>
          <div class="skeleton-button"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Actual Content -->
  <div *ngIf="!loadingStates.fetching && getCurrentItems().length > 0" class="ads-grid mt-3">
    <div class="modern-ad-card" *ngFor="let item of getCurrentItems()" [class.event-card]="getItemType(item) === 'event'">
      <!-- Status Badge -->
      <div class="status-badge" [class.expired]="getItemStatus(item).isExpired" [class.active]="!getItemStatus(item).isExpired">
        <i class="feather" [class]="getItemStatus(item).icon"></i>
        <span>{{ getItemStatus(item).statusText }}</span>
      </div>

      <!-- Card Header -->
      <div class="card-header">
        <div class="ad-icon">
          <i class="feather" [class]="getItemType(item) === 'event' ? 'icon-calendar' : 'icon-image'"></i>
        </div>
        <div class="ad-info">
          <h3 class="ad-title" [title]="getItemTitle(item)">{{ getItemTitle(item) }}</h3>
          <div class="ad-meta">
            <i class="feather icon-calendar"></i>
            <span>{{ getItemDateInfo(item) }}</span>
          </div>
        </div>
      </div>

      <!-- Image Preview (for both ads and events with images) -->
      <div class="image-preview" *ngIf="hasItemImage(item)">
        <img [nzSrc]="env.IP + '/' + getItemImage(item)" [alt]="getItemTitle(item)" class="ad-image" nz-image />
        <div class="image-overlay">
          <i class="feather icon-eye"></i>
        </div>
      </div>

      <!-- No Image Placeholder (for items without images) -->
      <div class="no-image-placeholder" *ngIf="!hasItemImage(item) && getItemType(item) === 'ad'">
        <i class="feather icon-image"></i>
        <span>No image uploaded</span>
      </div>

      <!-- Event Info Placeholder (for events without images) -->
      <div class="event-info-placeholder" *ngIf="!hasItemImage(item) && getItemType(item) === 'event'">
        <i class="feather icon-calendar"></i>
        <span>Event Details</span>
      </div>

      <!-- Description -->
      <div class="ad-description">
        <p class="description-text">
          {{ getTruncatedDescription(getItemDescription(item), item.id) }}
          <span *ngIf="shouldShowReadMore(getItemDescription(item), item.id)" class="ellipsis">...</span>
        </p>
        <button
          *ngIf="shouldShowReadMore(getItemDescription(item), item.id)"
          class="read-more-btn"
          (click)="toggleDescription(item.id)">
          <i class="feather icon-chevron-down"></i>
          <span>Read more</span>
        </button>
        <button
          *ngIf="shouldShowReadLess(getItemDescription(item), item.id)"
          class="read-more-btn"
          (click)="toggleDescription(item.id)">
          <i class="feather icon-chevron-up"></i>
          <span>Read less</span>
        </button>
      </div>

      <!-- Card Footer with Actions -->
      <div class="card-footer">
        <div class="footer-divider"></div>
        <div class="card-actions">
          <button class="action-btn edit-btn"
                  (click)="showModal(item)"
                  [title]="'Edit ' + getItemTitle(item)"
                  [disabled]="loadingStates.deleting">
            <i class="feather icon-edit"></i>
            <span>Edit</span>
          </button>
          <button class="action-btn delete-btn"
                  (click)="deleteItem(item)"
                  [title]="'Delete ' + getItemTitle(item)"
                  [disabled]="loadingStates.deleting"
                  [class.loading]="loadingStates.deleting">
            <i class="feather icon-trash-2"></i>
            <span>Delete</span>
          </button>
        </div>
      </div>
    </div>
  </div>


  <div *ngIf="!loadingStates.fetching && getCurrentItems().length === 0" class="no-data-message">
    <div class="empty-state">
      <i class="feather icon-inbox empty-icon"></i>
      <h3>No {{ activeTab === 0 ? 'Advertisements' : activeTab === 1 ? 'Events' : 'Items' }} Found</h3>
      <p>Create your first {{ activeTab === 0 ? 'advertisement' : activeTab === 1 ? 'event' : 'item' }} by clicking the buttons above.</p>
      <div class="empty-actions">
        <button (click)="showModal(undefined, 'ad')" class="btn btn-primary empty-action">
          <i class="feather icon-megaphone"></i> Create Advertisement
        </button>
        <button (click)="showModal(undefined, 'event')" class="btn btn-secondary empty-action">
          <i class="feather icon-calendar"></i> Create Event
        </button>
      </div>
    </div>
  </div>

  <div *ngIf="totalItems > pageSize" class="pagination-container">
    <nz-pagination
      [nzPageIndex]="currentPage"
      [nzPageSize]="pageSize"
      [nzTotal]="totalItems"
      [nzShowSizeChanger]="true"
      [nzPageSizeOptions]="[9, 18, 36]"
      (nzPageIndexChange)="onPageChange($event)"
      (nzPageSizeChange)="onPageSizeChange($event)">
    </nz-pagination>
  </div>
</div>

<nz-drawer
  [nzClosable]="false"
  [nzVisible]="isModalVisible"
  nzPlacement="right"
  (nzOnClose)="onDrawerClose()"
  [nzMaskClosable]="!loadingStates.creating && !loadingStates.updating"
  [nzKeyboard]="!loadingStates.creating && !loadingStates.updating"
  [nzMask]="true"
  class="modern-drawer-container"
  [nzWidth]="520"
>
  <ng-container *nzDrawerContent>
    <!-- Custom Header -->
    <div class="modern-drawer-header">
      <div class="header-content">
        <div class="header-icon">
          <i class="feather" [class]="currentAdId ? 'icon-edit' : 'icon-plus-circle'"></i>
        </div>
        <div class="header-text">
          <h3>
            {{ getDrawerTitle() }}
            <span *ngIf="loadingStates.creating || loadingStates.updating" class="loading-indicator">
              <nz-spin nzSize="small"></nz-spin>
            </span>
          </h3>
          <p>{{ getDrawerSubtitle() }}</p>
        </div>
      </div>
      <button class="close-btn"
              (click)="handleCancel()"
              [disabled]="loadingStates.creating || loadingStates.updating"
              [class.loading]="loadingStates.creating || loadingStates.updating">
        <nz-spin *ngIf="loadingStates.creating || loadingStates.updating" nzSize="small"></nz-spin>
        <i *ngIf="!loadingStates.creating && !loadingStates.updating" class="feather icon-x"></i>
      </button>
    </div>

    <!-- Loading Notice -->
    <div *ngIf="loadingStates.creating || loadingStates.updating" class="loading-notice">
      <div class="notice-content">
        <i class="feather icon-info"></i>
        <div class="notice-text">
          <strong>Operation in Progress</strong>
          <p>Please wait while we {{ loadingStates.creating ? 'create' : 'update' }} your {{ isEvent ? 'event' : 'advertisement' }}. The drawer cannot be closed during this process.</p>
        </div>
      </div>
    </div>

    <!-- Form Content -->
    <div class="modern-drawer-content">
      <form [formGroup]="adForm"
            class="ultra-modern-form"
            [class.loading]="loadingStates.creating || loadingStates.updating">

        <!-- Content Type Selection -->
        <div class="content-type-selector">
          <div class="selector-header">
            <h3>What would you like to create?</h3>
            <p>Choose the type of content you want to add</p>
          </div>

          <div class="type-options">
            <label class="type-option" [class.active]="!isEvent">
              <input
                type="radio"
                name="contentType"
                [value]="false"
                [(ngModel)]="isEvent"
                [ngModelOptions]="{standalone: true}"
                (change)="onContentTypeChange()"
              >
              <div class="option-card">
                <div class="option-icon ad-icon">
                  <i class="feather icon-megaphone"></i>
                </div>
                <div class="option-content">
                  <h4>Advertisement</h4>
                  <p>Promote products, services, or announcements</p>
                </div>
                <div class="option-check">
                  <i class="feather icon-check"></i>
                </div>
              </div>
            </label>

            <label class="type-option" [class.active]="isEvent">
              <input
                type="radio"
                name="contentType"
                [value]="true"
                [(ngModel)]="isEvent"
                [ngModelOptions]="{standalone: true}"
                (change)="onContentTypeChange()"
              >
              <div class="option-card">
                <div class="option-icon event-icon">
                  <i class="feather icon-calendar"></i>
                </div>
                <div class="option-content">
                  <h4>Event</h4>
                  <p>Share upcoming events, meetings, or activities</p>
                </div>
                <div class="option-check">
                  <i class="feather icon-check"></i>
                </div>
              </div>
            </label>
          </div>
        </div>

        <!-- Basic Information Section -->
        <div class="form-card">
          <div class="card-header">
            <div class="card-icon">
              <i class="feather icon-info"></i>
            </div>
            <div class="card-title">
              <h4>Basic Information</h4>
              <span>Essential details for your advertisement</span>
            </div>
          </div>

          <div class="form-fields">
            <div class="field-group">
              <label class="modern-label">
                <i class="feather icon-type"></i>
                <span>{{ isEvent ? 'Event Title' : 'Advertisement Title' }}</span>
                <span class="required">*</span>
              </label>
              <div class="input-wrapper">
                <input
                  type="text"
                  formControlName="title"
                  class="modern-input"
                  [placeholder]="isEvent ? 'Enter a compelling title for your event' : 'Enter a catchy title for your ad'"
                />
                <div class="input-border"></div>
              </div>
              <div class="field-error" *ngIf="adForm.get('title')?.invalid && adForm.get('title')?.touched">
                Please enter a title for your {{ isEvent ? 'event' : 'advertisement' }}
              </div>
            </div>

            <div class="field-group">
              <label class="modern-label">
                <i class="feather icon-file-text"></i>
                <span>{{ isEvent ? 'Event Description' : 'Advertisement Description' }}</span>
                <span class="required">*</span>
              </label>
              <div class="input-wrapper">
                <textarea
                  formControlName="description"
                  class="modern-textarea"
                  [placeholder]="isEvent ? 'Describe your event details, agenda, and what attendees can expect...' : 'Describe your advertisement in detail...'"
                  rows="4"
                ></textarea>
                <div class="input-border"></div>
              </div>
              <div class="field-error" *ngIf="adForm.get('description')?.invalid && adForm.get('description')?.touched">
                Please provide a description for your {{ isEvent ? 'event' : 'advertisement' }}
              </div>
            </div>

            <!-- Date Fields - Different for Ads vs Events -->
            <div *ngIf="!isEvent" class="field-group">
              <label class="modern-label">
                <i class="feather icon-calendar"></i>
                <span>Expiration Date (Ethiopian Calendar)</span>
                <span class="required">*</span>
              </label>
              <div class="input-wrapper">
                <app-ethiopian-datepicker
                  formControlName="expireAt"
                  label="Select Ethiopian expiration date"
                  (dateChange)="onDateChange($event)"
                ></app-ethiopian-datepicker>
                <div class="input-border"></div>
              </div>
              <div class="field-error" *ngIf="adForm.get('expireAt')?.invalid && adForm.get('expireAt')?.touched">
                <div *ngIf="adForm.get('expireAt')?.errors?.['required']">
                  Please select an expiration date
                </div>
                <div *ngIf="adForm.get('expireAt')?.errors?.['pastDate']">
                  Cannot select a date in the past
                </div>
              </div>
            </div>

            <!-- Event Date Info -->
            <div *ngIf="isEvent" class="event-date-info">
              <div class="info-card">
                <i class="feather icon-info"></i>
                <div class="info-content">
                  <h5>Event Duration</h5>
                  <p>Specify when your event starts and ends using the Ethiopian calendar</p>
                </div>
              </div>
            </div>

            <!-- Event Start Date -->
            <div *ngIf="isEvent" class="field-group">
              <label class="modern-label">
                <i class="feather icon-calendar"></i>
                <span>Event Start Date (Ethiopian Calendar)</span>
                <span class="required">*</span>
              </label>
              <div class="input-wrapper">
                <app-ethiopian-datepicker
                  formControlName="startDate"
                  label="Select Ethiopian event start date"
                  (dateChange)="onDateChange($event)"
                ></app-ethiopian-datepicker>
                <div class="input-border"></div>
              </div>
              <div class="field-error" *ngIf="adForm.get('startDate')?.invalid && adForm.get('startDate')?.touched">
                <div *ngIf="adForm.get('startDate')?.errors?.['required']">
                  Please select an event start date
                </div>
                <div *ngIf="adForm.get('startDate')?.errors?.['pastDate']">
                  Cannot select a date in the past
                </div>
              </div>
            </div>

            <!-- Event End Date -->
            <div *ngIf="isEvent" class="field-group">
              <label class="modern-label">
                <i class="feather icon-calendar"></i>
                <span>Event End Date (Ethiopian Calendar)</span>
                <span class="required">*</span>
              </label>
              <div class="input-wrapper">
                <app-ethiopian-datepicker
                  formControlName="endDate"
                  label="Select Ethiopian event end date"
                  (dateChange)="onDateChange($event)"
                ></app-ethiopian-datepicker>
                <div class="input-border"></div>
              </div>
              <div class="field-error" *ngIf="adForm.get('endDate')?.invalid && adForm.get('endDate')?.touched">
                Please select an event end date
              </div>
            </div>

            <!-- Event Status (for events only) -->

          </div>
        </div>

        <!-- Media Upload Section (for both ads and events) -->
        <div class="form-card">
          <div class="card-header">
            <div class="card-icon">
              <i class="feather icon-image"></i>
            </div>
            <div class="card-title">
              <h4>Media Content</h4>
              <span>Upload images or media files for your {{ isEvent ? 'event' : 'advertisement' }}</span>
            </div>
          </div>

          <div class="upload-section">
            <!-- Show existing image for editing -->
            <div class="existing-image" *ngIf="currentAdId || currentEventId">
              <div class="existing-image-preview" *ngIf="getExistingImageUrl()">
                <img [src]="env.IP + '/' + getExistingImageUrl()" [alt]="'Current ' + (isEvent ? 'event' : 'ad') + ' image'" class="current-image" />
                <div class="existing-image-overlay">
                  <span>Current Image</span>
                </div>
              </div>
              <p class="existing-image-text" *ngIf="getExistingImageUrl()">
                Upload a new image to replace the current one, or leave empty to keep the existing image.
              </p>
              <p class="no-existing-image-text" *ngIf="!getExistingImageUrl()">
                No image currently set. Upload an image below.
              </p>
            </div>

            <div class="upload-area" [class.has-files]="fileList.length > 0">
              <!-- Try basic upload first -->
              <nz-upload
                [nzFileList]="fileList"
                [nzBeforeUpload]="beforeUpload"
                nzMultiple="false"
                nzAccept="image/*"
                nzListType="picture-card"
                class="modern-upload"
              >
                <div *ngIf="fileList.length < 1">
                  <div class="upload-content">
                    <div class="upload-icon">
                      <i class="feather icon-upload-cloud"></i>
                    </div>
                    <div class="upload-text">
                      <p class="upload-title">Drop image here or click to upload</p>
                      <p class="upload-subtitle">JPG, PNG, GIF, WebP (Max 5MB)</p>
                    </div>
                  </div>
                </div>
              </nz-upload>

              <!-- Selected file info -->
              <div *ngIf="fileList.length > 0" class="selected-file-info">
                <div class="info-header">
                  <i class="feather icon-check-circle"></i>
                  <span>Selected Image</span>
                </div>
                <div *ngFor="let file of fileList" class="file-summary">
                  <div class="file-details">
                    <span class="file-name">{{ file.name }}</span>
                    <span class="file-size">{{ getFileSize(file) }}</span>
                  </div>
                  <button type="button" (click)="removeFileManually(file)" class="remove-file-btn">
                    <i class="feather icon-x"></i>
                  </button>
                </div>
              </div>
            </div>

            <!-- File validation info -->
            <div class="upload-info">
              <div class="info-header">
                <i class="feather icon-shield"></i>
                <span>Upload Requirements</span>
              </div>
              <div class="info-items">
                <div class="info-item">
                  <i class="feather icon-check-circle"></i>
                  <span>Only one image file allowed</span>
                </div>
                <div class="info-item">
                  <i class="feather icon-image"></i>
                  <span>Formats: JPG, PNG, GIF, WebP</span>
                </div>
                <div class="info-item">
                  <i class="feather icon-hard-drive"></i>
                  <span>Max size: 5MB</span>
                </div>
                <div class="info-item">
                  <i class="feather icon-maximize-2"></i>
                  <span>Dimensions: 100x100px - 4000x4000px</span>
                </div>
              </div>
            </div>

            <!-- Current file status -->
            <div *ngIf="fileList.length > 0" class="file-status">
              <div class="status-header">
                <i class="feather icon-file"></i>
                <span>Selected File</span>
              </div>
              <div class="file-details" *ngFor="let file of fileList">
                <div class="file-info">
                  <span class="file-name">{{ file.name }}</span>
                  <span class="file-size">{{ getFileSize(file) }}</span>
                </div>
                <div class="file-actions">
                  <button type="button" class="preview-btn" (click)="onPreviewFile(file)" *ngIf="file.status === 'done'">
                    <i class="feather icon-eye"></i>
                    Preview
                  </button>
                  <button type="button" class="remove-btn" (click)="onRemoveFile(file)">
                    <i class="feather icon-trash-2"></i>
                    Remove
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

      </form>
    </div>

    <!-- Modern Footer -->
    <div class="modern-drawer-footer">
      <div class="footer-actions">
        <button class="modern-btn btn-secondary"
                (click)="handleCancel()"
                [disabled]="loadingStates.creating || loadingStates.updating">
          <i class="feather icon-x"></i>
          <span>Cancel</span>
        </button>
        <button class="modern-btn btn-primary" (click)="handleOk()"
                [disabled]="adForm.invalid || loadingStates.creating || loadingStates.updating"
                [class.loading]="loadingStates.creating || loadingStates.updating">
          <nz-spin *ngIf="loadingStates.creating || loadingStates.updating" nzSize="small"></nz-spin>
          <i *ngIf="!loadingStates.creating && !loadingStates.updating" class="feather" [class]="currentAdId ? 'icon-check' : 'icon-plus'"></i>
          <span>{{ getSubmitButtonText() }}</span>
        </button>
      </div>
    </div>
  </ng-container>
</nz-drawer>
