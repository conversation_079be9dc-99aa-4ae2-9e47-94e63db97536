import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NzModalService } from 'ng-zorro-antd/modal';

import AdsComponent from './ads.component';
import { Ad } from './ads.service';

describe('AdsComponent', () => {
  let component: AdsComponent;
  let fixture: ComponentFixture<AdsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AdsComponent, ReactiveFormsModule, HttpClientTestingModule],
      providers: [NzModalService]
    })
    .compileComponents();

    fixture = TestBed.createComponent(AdsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should set Ethiopian date when editing an ad', () => {
    // Mock ad data with Ethiopian date
    const mockAd: Ad = {
      id: 1,
      title: 'Test Ad',
      description: 'Test Description',
      expireAt: new Date(),
      expiredAtEthiopian: [2017, 11, 22], // Ethiopian date array
      isExpired: false
    };

    // Call showModal with the mock ad
    component.showModal(mockAd);

    // Check if the form control has the correct Ethiopian date format
    const expireAtValue = component.adForm.get('expireAt')?.value;
    expect(expireAtValue).toBeDefined();
    expect(expireAtValue.ethiopian).toBeDefined();
    expect(expireAtValue.ethiopian.year).toBe(2017);
    expect(expireAtValue.ethiopian.month).toBe(11);
    expect(expireAtValue.ethiopian.day).toBe(22);
  });

  it('should handle ad without Ethiopian date', () => {
    // Mock ad data without Ethiopian date
    const mockAd: Ad = {
      id: 1,
      title: 'Test Ad',
      description: 'Test Description',
      expireAt: new Date(),
      isExpired: false
    };

    // Call showModal with the mock ad
    component.showModal(mockAd);

    // Check if the form is populated correctly
    expect(component.adForm.get('title')?.value).toBe('Test Ad');
    expect(component.adForm.get('description')?.value).toBe('Test Description');
  });
});
