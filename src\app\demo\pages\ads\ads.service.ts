import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

export interface Ad {
  id: number;
  image?: string;
  title: string;
  description: string;
  expireAt: Date;
  expiredAtEthiopian?: [number, number, number]; // Ethiopian date as [year, month, day]
  isExpired: boolean;
}

export interface Event {
  id: number;
  image_url?: string; // Added image support for events
  title: string;
  desciption: string; // Note: matches backend API field name
  start_date: string;
  end_date: string;
  status: number;
  createdAt: string;
  updatedAt: string;
  postedby: number;
  startDateEthiopian?: [number, number, number];
  endDateEthiopian?: [number, number, number];
}

export interface EventsResponse {
  data: Event[];
  pagination: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
  };
}

export interface ActiveEventsResponse {
  success: boolean;
  message: string;
  data: Event[];
}

@Injectable({
  providedIn: 'root'
})
export class AdsService {
  private adsApiUrl = environment.URL + '/ads';
  private eventsApiUrl = environment.URL + '/events';

  constructor(private http: HttpClient) { }

  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('token');
    return new HttpHeaders().set('Authorization', `Bearer ${token}`);
  }

  // Ads API methods
  getAds(page: number, limit: number): Observable<{ data: Ad[], total: number }> {
    return this.http.get<{ data: Ad[], total: number }>(`${this.adsApiUrl}?page=${page}&limit=${limit}`, { headers: this.getHeaders() });
  }

  createAd(adData: FormData): Observable<Ad> {
    return this.http.post<Ad>(this.adsApiUrl, adData, { headers: this.getHeaders() });
  }

  updateAd(id: number, adData: FormData): Observable<Ad> {
    return this.http.put<Ad>(`${this.adsApiUrl}/${id}`, adData, { headers: this.getHeaders() });
  }

  deleteAd(id: number): Observable<void> {
    return this.http.delete<void>(`${this.adsApiUrl}/${id}`, { headers: this.getHeaders() });
  }

  // Events API methods
  getEvents(page: number, limit: number): Observable<EventsResponse> {
    return this.http.get<EventsResponse>(`${this.eventsApiUrl}?page=${page}&limit=${limit}`, { headers: this.getHeaders() });
  }

  getEventById(id: number): Observable<Event> {
    return this.http.get<Event>(`${this.eventsApiUrl}/${id}`, { headers: this.getHeaders() });
  }

  createEvent(eventData: FormData): Observable<Event> {
    return this.http.post<Event>(this.eventsApiUrl, eventData, { headers: this.getHeaders() });
  }

  updateEvent(id: number, eventData: FormData): Observable<{ message: string }> {
    return this.http.put<{ message: string }>(`${this.eventsApiUrl}/${id}`, eventData, { headers: this.getHeaders() });
  }

  deleteEvent(id: number): Observable<void> {
    return this.http.delete<void>(`${this.eventsApiUrl}/${id}`, { headers: this.getHeaders() });
  }

  getActiveEvents(): Observable<ActiveEventsResponse> {
    return this.http.get<ActiveEventsResponse>(`${this.eventsApiUrl}/active`, { headers: this.getHeaders() });
  }
}
