import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../../../../environments/environment';
import { StorageService } from 'src/app/theme/shared/service/storage.service';


@Injectable({
  providedIn: 'root'
})
export class AuthService {

  constructor(private http: HttpClient,private storageService: StorageService) { }

  httpOptions = {
    header: new HttpHeaders({
      'Content-Type': 'application/json',
    })
  };

  SignIn(userData:any){
    return this.http.post<any>(
      environment.URL + `/login`,
      userData,
      {
        headers: this.httpOptions.header,
      }
    );
  }

  GetProfile(token){
    const httpOptions = {
      headers: new HttpHeaders({
        Authorization: `Bearer ${token}`,
      }),
    }
    return this.http.get<any>(
      environment.URL + `/profile`,
      {
        headers: httpOptions.headers,
      }
    );
  }

  isLoggedIn(): boolean {
    const authToken = this.storageService.getToken();
    return authToken !== null && authToken !== undefined;
  }

}
