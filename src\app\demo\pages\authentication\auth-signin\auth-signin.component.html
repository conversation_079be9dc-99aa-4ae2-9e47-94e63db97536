<!-- <div class="auth-wrapper">
  <div class="auth-content">
    <div class="auth-bg">
      <span class="r"></span>
      <span class="r s"></span>
      <span class="r s"></span>
      <span class="r"></span>
    </div>
    <div class="card">
      <form  (ngSubmit)="signIn()" (keydown.enter)="$event.preventDefault(); signIn()">
      <div class="card-body text-center">
        <div class="mb-4">
          <i class="feather icon-unlock auth-icon"></i>
        </div>
        <h3 class="mb-4">Login</h3>
        <div class="input-group mb-3">
          <input type="text" name="username" [(ngModel)]="username" class="form-control" placeholder="የተጠቃሚ ስም" />
        </div>
        <div class="input-group mb-4">
          <input type="password" name="password" [(ngModel)]="password" class="form-control" placeholder="የይለፍ ቃል" />
        </div>
         <nz-alert *ngIf="error" nzType="error" [nzMessage]="error"></nz-alert>
         <button class="btn btn-primary submit-button mt-3" type="submit">
          <nz-spin *ngIf="loading" nzSimple></nz-spin>
          ግባ
        </button>

      </div>
      </form>
    </div>
  </div>
</div> -->

<div class="modern-auth-wrapper">
  <div class="auth-content">
    <div class="auth-bg-modern"> <!-- Renamed for clarity -->
      <span class="shape s1"></span>
      <span class="shape s2"></span>
      <span class="shape s3"></span>
      <span class="shape s4"></span>
    </div>
    <div class="card modern-card">
      <form (ngSubmit)="signIn()" (keydown.enter)="$event.preventDefault(); signIn()">
        <div class="card-body text-center">
          <div class="mb-4">
            <i class="feather icon-unlock auth-icon"></i>
          </div>
          <h3 class="mb-4 card-title">Login</h3>
          <div class="form-group mb-3">
            <input type="text" name="username" [(ngModel)]="username" class="form-control modern-input" placeholder="Username" required />
          </div>
          <div class="form-group mb-4">
            <input type="password" name="password" [(ngModel)]="password" class="form-control modern-input" placeholder="Password" required />
          </div>

          <nz-alert *ngIf="error" nzType="error" [nzMessage]="error" class="mb-3"></nz-alert>

          <button class="btn btn-primary submit-button w-100 mt-3" type="submit" [disabled]="loading">
            <nz-spin *ngIf="loading" nzSimple class="button-spinner"></nz-spin>
            <span *ngIf="!loading">Login</span>
            <span *ngIf="loading">Logging in...</span>
          </button>

        </div>
      </form>
    </div>
    <p class="text-center mt-3 " style="color: rgba(255,255,255,0.7);">
      © {{2025}} Ethiopian Broadcasting Corporation
    </p>
  </div>
</div>
