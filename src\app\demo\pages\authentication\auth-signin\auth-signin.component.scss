/* Import a modern font (add to your index.html or angular.json styles) */
/* @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap'); */

/* Make sure to add this if you haven't:
   In your global styles (styles.scss or styles.css):
   @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
   body { font-family: 'Poppins', sans-serif; }
*/
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

body {
  font-family: 'Poppins', sans-serif;
  /* Other global styles */
}
/* Import a modern font (add to your index.html or angular.json styles) */
/* @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap'); */

:host {
  display: block;
  height: 100vh;
}

.modern-auth-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  background-color: #3d1447; /* Your chosen deep purple background */
  font-family: 'Poppins', sans-serif;
  position: relative;
  /* Optional: Add subtle noise or very faint pattern if desired
  background-image: url('data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 2 2"><path d="M1 2V0h1v1H0v1z" fill-opacity=".02" fill="white"/></svg>');
  background-size: 3px 3px;
  */
}

.auth-content {
  width: 100%;
  max-width: 480px; /* Or your preferred wider width */
  position: relative;
  z-index: 1;
}

.modern-card {
  background: #ffffff; /* Clean white card */
  /* Or a slightly off-white: background: #f8f9fa; */
  /* Or slightly transparent: background: rgba(255, 255, 255, 0.98); */
  border: none;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2); /* Adjusted shadow for dark BG */
  overflow: hidden;
}

.card-body {
  padding: 30px 35px;
}

.auth-icon {
  font-size: 48px;
  color: #8a5db3; /* A lighter, complementary purple for the icon */
  /* Alternative: A muted gold: color: #c0a062; */
}

.card-title {
  font-weight: 600;
  color: #333;
  font-size: 1.75rem;
}

.modern-input.form-control {
  background-color: #f7f7f9; /* Light background for inputs */
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px 15px;
  font-size: 0.95rem;
  color: #333; /* Dark text color for input */
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.modern-input.form-control::placeholder {
  color: #888; /* Slightly darker placeholder for better contrast on light input */
}

.modern-input.form-control:focus {
  background-color: #fff;
  border-color: #8a5db3; /* Accent color on focus (matches icon) */
  box-shadow: 0 0 0 0.2rem rgba(138, 93, 179, 0.25);
  outline: none;
}

nz-alert.mb-3 { /* Ensure specificity if needed */
  border-radius: 8px;
  text-align: left;
}
/* Ensure nz-alert text is readable */
:host ::ng-deep nz-alert .ant-alert-message {
    color: #c7254e; /* Default Ant error text, should be fine */
}
:host ::ng-deep nz-alert.ant-alert-error {
    background-color: #f8d7da; /* Default Ant error background, should be fine */
    border-color: #f5c6cb;
}


.submit-button.btn-primary {
  background-color: #6a3093; /* A vibrant purple for the button, slightly lighter than BG */
  /* Alternative button color: A contrasting teal/cyan: #4FC3F7 */
  /* Alternative button color: A magenta: #c83c83 */
  border-color: transparent; /* No border or same as background */
  color: #ffffff; /* White text on button */
  padding: 12px 20px;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 8px;
  transition: background-color 0.2s ease-in-out, transform 0.15s ease-out;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.submit-button.btn-primary:hover:not(:disabled) {
  background-color: #58287d; /* Darker shade of button color on hover */
  transform: translateY(-2px);
}

.submit-button.btn-primary:disabled {
  background-color: #a07bb5; /* Muted purple for disabled state */
  border-color: #a07bb5;
  color: rgba(255,255,255,0.7);
  box-shadow: none;
  cursor: not-allowed;
}

.button-spinner {
  margin-right: 8px;
}
.button-spinner :global(.ant-spin-dot-item) {
  background-color: white !important;
}

/* Footer text styling */
.text-center.mt-3[style*="color"] { /* More specific selector for the footer text */
  color: rgba(255, 255, 255, 0.7) !important; /* Light color for footer on dark BG */
}
