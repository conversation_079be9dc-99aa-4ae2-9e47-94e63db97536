import { Component } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { NavigationItem, NavigationItems } from 'src/app/theme/layout/admin/navigation/navigation';
import { AuthService } from '../auth-service.service';
import { StorageService } from 'src/app/theme/shared/service/storage.service';
import { SharedModule } from 'src/app/theme/shared/shared.module';
import PermissionService from 'src/app/theme/shared/service/permission.service';

@Component({
  selector: 'app-auth-signin',
  standalone: true,
  imports: [RouterModule,SharedModule],
  templateUrl: './auth-signin.component.html',
  styleUrls: ['./auth-signin.component.scss']
})
export default class AuthSigninComponent {

  username:any;
  password:any;

  error:any;
  permissions:any[] =[];
  navigation:any;
  public nav:any = NavigationItems;
  loading:boolean = false;

  constructor(private router: Router,private authservice:AuthService
      ,private storage:StorageService, private service:PermissionService){}


    async signIn(){
      let data= {
        username:this.username,
        password:this.password
      }
      this.loading = true;

     await this.authservice.SignIn(data).subscribe({
        next:async (result:any)=>{
          this.loading = false;
          if(result.status == 200){
            await this.setTokes(result.data.token);
            await this.getProfile(result.data.token);

          }
        },
        error:(err:any)=>{
          this.loading = false;
          this.error = err.error.message;

        }
      })

    }


    async getProfile(token:string){
      await this.authservice.GetProfile(token).subscribe({
        next:async (result:any)=>{
          if(result){
            await this.setUserId(result.data.id);
            await this.GetPermissions(result.data.id);
          }
        },
        error:(err:any)=>{
          this.loading = false;
          this.error = err.error.message;
        }
      })
    }


    async setTokes(token:any){
      await this.storage.setToken(token);
    }

    async setUserId(userId:any){
      await this.storage.setUserId(userId)
    }

    filterNavMenu(menu: any[], permissions: string[]): any[] {
      return menu.reduce((filteredMenu, item) => {
        if (permissions.includes(item.id) || (item.children && item.children.length > 0)) {
          const newItem: any = { ...item };
          if (item.children) {
            newItem.children = this.filterNavMenu(item.children, permissions);
          }
          filteredMenu.push(newItem);
        }
        return this.removeEmptyParents(filteredMenu, permissions);
      }, [] as any[]);
    }

    removeEmptyParents(menu: any[], permissions: string[]): any[] {
      return menu.filter(item => {
        if (item.children && item.children.length > 0) {
          item.children = this.removeEmptyParents(item.children, permissions);
          return true;
        } else {
          return permissions.includes(item.id);
        }
      });
    }

    async GetPermissions(userId) {
     await this.service.getPermissionsByUserId(userId).subscribe((per: any) => {
     
        let p = per.data;
        if (p.length > 0) {
          p.forEach((pers: any) => {
            this.permissions.push(pers.nav_menuId);
          });
          const resultArray = this.removeElementsFromArray(p,  this.permissions);
          this.navigation = this.filterNavMenu(this.nav, resultArray.length>0?resultArray: this.permissions);


          const isDashboardAllowed = this.navigation[0].children
          .find((n: any) => n.id === 'dashboard');
          if(isDashboardAllowed) {
            this.router.navigate(['dashboard']);
          }else{
            const navItems = this.navigation[0].children;
            let url:any;
            if(!navItems[0].children){
              url =  navItems[0].url;
              this.router.navigate([url]);
            }else{
              const childrenItems = navItems[0].children[0];
              url =  childrenItems.url;
              this.router.navigate([url]);
            }
          }

        }

      });
    }

    removeElementsFromArray(array1, array2) {
      // Use filter to keep only the elements not present in array1
      const resultArray = array2.filter(element => array1.includes(element));
      return resultArray;
  }
}
