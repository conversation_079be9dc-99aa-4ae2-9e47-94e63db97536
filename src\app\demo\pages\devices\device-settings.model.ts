export interface DeviceSettings {
  id?: number | string;
  name?: string; // Optional now as we'll use default_Id instead
  device?: string; // References devices.id
  userId?: string;
  default_Id?: number; // References id from /default/settings
  file?: File | string | null;
  filePath?: string;
  createdAt?: string | Date;
  updatedAt?: string | Date;
}

export interface DefaultSetting {
  id: number;
  name: string;
  type: string; // 'animation' or 'video'
}
