<app-validate-permission  [pageId]="'devices'" (permissionChecked)="isPageAllowed = $event"></app-validate-permission>
<div *ngIf="isPageAllowed" class="row">
  <div class="col-sm-12">
    <app-card cardTitle="Hello Card">
      <!-- src/app/routes/devices/devices.component.html -->
<div>
  <h2>Device Management</h2> <!-- Optional Title -->

  <nz-table
    #deviceTableRef
    [nzData]="devices"
    [nzLoading]="isLoading"
    nzTableLayout="fixed"
    [nzFrontPagination]="true"
    [nzShowSizeChanger]="true"
    [nzPageSizeOptions]="[10, 20, 50, 100]"
    [nzPageSize]="10"
    nzBordered
    nzSize="middle"
    [nzScroll]="{ x: '1000px' }"
  >
    <thead>
      <tr>
        <!-- Loop through column definitions -->
        <th
          *ngFor="let column of listOfColumns"
          [nzSortOrder]="column.sortOrder"
          [nzSortFn]="column.sortFn"
          [nzSortDirections]="column.sortDirections"
          [nzFilterMultiple]="column.filterMultiple === undefined ? true : column.filterMultiple"
          [nzFilters]="column.listOfFilter || []"
          [nzShowFilter]="(column.listOfFilter || []).length > 0"
          (nzFilterChange)="column.filterFn ? column.filterFn($event, column) : null"
            [nzWidth]="column.width"
            [hidden]="column.key === 'actions' && !devices.length"
        >
          {{ column.name }}
        </th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of deviceTableRef.data; trackBy: trackById">
        <td>{{ data.id }}</td>
        <td>
          <a (click)="viewDevice(data)" class="device-name-link" title="View {{ data.name }} details">{{ data.name }}</a>
        </td>
        <td>{{ data.deviceId }}</td>
        <td>
          <nz-badge [nzStatus]="getStatusType(data.status)" [nzText]="data.status | titlecase"></nz-badge>
        </td>
        <td>

          {{ datePipe.transform(data.createdAt, 'mediumDate') }}
          <span class="time-subtext">{{ datePipe.transform(data.createdAt, 'shortTime') }}</span>
        </td>
        <td>
           {{ datePipe.transform(data.updatedAt, 'mediumDate') }}
           <span class="time-subtext">{{ datePipe.transform(data.updatedAt, 'shortTime') }}</span>
        </td>
        <td>

           <button nz-button nzType="default" nzShape="circle" nzSize="small" (click)="openModal(data)" nz-tooltip nzTooltipTitle="View Details">
            <i class="feather icon-settings"></i>
          </button>
          <nz-divider nzType="vertical"></nz-divider>

        </td>
      </tr>
    </tbody>



  </nz-table>
  </div>
    </app-card>
  </div>
</div>

<div class="modal fade" id="deviceSettings" tabindex="-1" aria-labelledby="deviceSettingsLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <div class="d-flex flex-row justify-content-between align-items-center">
          <i class="feather icon-monitor" style="font-size: 28px; color: black;"></i>
          <div class="d-flex flex-column justify-content-between align-items-center" style="margin-left: 10px;">
            <span style="font-size: 18px; color: black;">{{selectedDevice?.name}}</span>
            <span style="font-size: 12px; color: gray;">{{selectedDevice?.deviceId}}</span>
         </div>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <!-- Loading indicator -->
        <div *ngIf="isLoadingSettings" class="text-center p-4">
          <nz-spin nzTip="Loading device settings..."></nz-spin>
        </div>

        <!-- No settings message -->
        <div *ngIf="!isLoadingSettings && (!deviceSettings || deviceSettings.length === 0)" class="alert alert-info d-flex justify-content-between align-items-center">
          <span>No device settings found for this device.</span>
          <button nz-button nzType="primary" (click)="initNewSetting()" nz-tooltip nzTooltipTitle="Create New Setting">
            <i nz-icon nzType="plus"></i> Create Setting
          </button>
        </div>

        <!-- Settings list -->
        <div *ngIf="!isLoadingSettings && deviceSettings && deviceSettings.length > 0" class="mb-4">
          <div class="d-flex justify-content-between align-items-center mb-2">
            <h5 class="mb-0">Available Settings</h5>
            <button nz-button nzType="primary" (click)="initNewSetting()" nz-tooltip nzTooltipTitle="Create New Setting">
              <i nz-icon nzType="plus"></i> New Setting
            </button>
          </div>
          <div class="settings-list">
            <div *ngFor="let setting of deviceSettings" class="setting-item d-flex justify-content-between align-items-center p-2 mb-2 border rounded"
                 [class.selected-setting]="selectedSetting === setting">
              <div class="d-flex align-items-center" (click)="selectSetting(setting)" style="cursor: pointer;">
                <label nz-radio [ngModel]="selectedSetting === setting"></label>
                <span class="setting-name">
                  {{ getDefaultSettingName(setting.default_Id) }}
                </span>
              </div>

              <div class="setting-actions">
                <button nz-button nzType="primary" nzDanger nzShape="circle" nzSize="small"
                        (click)="deleteDeviceSettings(setting)"
                        nz-popconfirm
                        nzPopconfirmTitle="Are you sure you want to delete this setting?"
                        nzPopconfirmPlacement="left"
                        nz-tooltip nzTooltipTitle="Delete">
                  <i nz-icon nzType="delete" nzTheme="outline"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Create/Edit Form -->
        <div *ngIf="!isLoadingSettings" class="mt-4">
          <h5>{{selectedSetting && selectedSetting.id ? 'Edit Setting' : 'Create New Setting'}}</h5>

          <div class="mb-3">
            <label class="form-label">Setting Type</label>
            <div class="d-flex flex-column">
              <nz-radio-group [(ngModel)]="selectedDefaultSetting" (ngModelChange)="onDefaultSettingChange($event)">
                <label nz-radio *ngFor="let setting of defaultSettings" [nzValue]="setting">
                  {{setting.name}}
                </label>
              </nz-radio-group>
            </div>
          </div>

          <div class="mb-3" *ngIf="selectedDefaultSetting && selectedDefaultSetting.type === 'video'">
            <label for="settingFile" class="form-label">Upload Video</label>
            <input type="file" class="form-control" id="settingFile" (change)="onFileSelected($event)" accept="video/*">
            <small *ngIf="selectedSetting && selectedSetting.filePath" class="text-muted">
              Current file: {{selectedSetting.filePath}}
            </small>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" (click)="saveDeviceSettings()" [disabled]="isLoadingSettings">
          <span *ngIf="selectedSetting && selectedSetting.id">Update</span>
          <span *ngIf="!selectedSetting || !selectedSetting.id">Create</span>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Add some CSS for the settings list -->
<style>
  .settings-list {
    max-height: 200px;
    overflow-y: auto;
  }

  .setting-item {
    transition: background-color 0.2s;
  }

  .setting-item:hover {
    background-color: #f5f5f5;
  }

  .selected-setting {
    background-color: #e6f7ff;
    border-color: #1890ff !important;
  }

  .setting-name {
    font-weight: 500;
    margin-left: 8px;
  }
</style>
