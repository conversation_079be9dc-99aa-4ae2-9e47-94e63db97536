// src/app/routes/devices/devices.component.scss

:host {
  display: block;
  padding: 24px; // Space around the component
  background-color: #f0f2f5; // Light page background
  min-height: calc(100vh - 100px); // Example: Adjust based on your layout/header height
}

.table-container {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden; // Keeps table corners rounded
  padding: 24px; // Inner padding for the card
}

h2 {
  margin-bottom: 20px;
  font-size: 1.4em;
  font-weight: 600;
  color: #333;
}

// Ant Design Table Customizations
// Using ::ng-deep as AntD elements are complex children. Be specific if possible.
:host ::ng-deep .ant-table {
  font-size: 14px;

  .ant-table-thead > tr > th {
    background-color: #fafafa;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    border-bottom: 1px solid #e8e8e8;
    padding: 14px 16px; // Slightly more vertical padding in header
    white-space: nowrap; // Prevent header text wrapping
  }

  .ant-table-tbody > tr > td {
    border-bottom: 1px solid #f0f0f0;
    padding: 12px 16px;
    vertical-align: middle; // Align content vertically
  }

  .ant-table-tbody > tr:hover > td {
    background-color: #e6f7ff; // Standard hover color
  }

  // Nicer empty state message
  .ant-empty-description {
      color: #888;
  }

  // Badge styling
  .ant-badge-status-text {
    margin-left: 8px;
    font-size: 13px;
    text-transform: capitalize; // Handled by pipe now, but good fallback
  }
}

// Specific column styling
.device-name-link {
  color: #1890ff; // AntD primary color
  cursor: pointer;
  font-weight: 500;
  &:hover {
    text-decoration: underline;
  }
}

.time-subtext {
  display: block;
  font-size: 0.85em;
  color: #888;
  margin-top: 3px;
}

// Action buttons styling
:host ::ng-deep .ant-table-tbody td:last-child {
  text-align: center; // Center buttons nicely
  white-space: nowrap; // Prevent action buttons from wrapping
}

:host ::ng-deep .ant-table-tbody button {
  margin: 0 5px; // Slightly more space between buttons
}

// Responsive considerations (AntD table handles horizontal scroll well with nzScroll)
@media (max-width: 768px) {
   h2 {
     font-size: 1.2em;
   }
   // Add more specific mobile styles if needed
}
