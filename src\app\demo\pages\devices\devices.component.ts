// src/app/routes/devices/devices.component.ts (Your existing file)

import { CommonModule, DatePipe } from '@angular/common'; // DatePipe might be implicitly available via SharedModule but explicit import is fine
import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { SharedModule } from 'src/app/theme/shared/shared.module'; // Your Shared Module
import { Device } from './device.model'; // Your Device Interface (adjust path)
import { DeviceSettings } from './device-settings.model'; // Device Settings Interface
import { NzTableSortFn, NzTableSortOrder } from 'ng-zorro-antd/table';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { finalize } from 'rxjs/operators';
import { DevicesService } from './devices.service';
import * as bootstrap from 'bootstrap';
import { NzMessageService } from 'ng-zorro-antd/message';
import ValidatePermissionComponent from "../../../theme/shared/components/validate-permission/validate-permission.component";

// Interface for column definition (can be kept inside component or moved)
interface ColumnItem {
  name: string;
  key: keyof Device | 'actions'; // Use keyof Device for type safety
  sortOrder?: NzTableSortOrder | null;
  sortFn?: NzTableSortFn<Device> | null;
  sortDirections?: NzTableSortOrder[];
  filterMultiple?: boolean;
  listOfFilter?: Array<{ text: string; value: any }>;
  filterFn?: (list: any[], item: any) => boolean | null;
  width?: string;
}

@Component({
  selector: 'app-devices',
  standalone: true, // This component is standalone
  imports: [
    CommonModule,
    FormsModule,
    SharedModule, // Import SharedModule to get all Angular and AntD features
    NzRadioModule,
    NzSpinModule,
    NzPopconfirmModule,
    NzButtonModule,
    NzIconModule,
    NzToolTipModule,
    ValidatePermissionComponent
],
  templateUrl: './devices.component.html',
  styleUrls: ['./devices.component.scss'], // Note: Use styleUrls for array
  changeDetection: ChangeDetectionStrategy.OnPush, // Good practice for performance
  // providers: [DatePipe] // DatePipe is now provided by SharedModule
})
export default class DevicesComponent implements OnInit { // Export default might be specific to your routing setup

  devices: Device[] = [];
  isPageAllowed:any;
  isLoading = true;
  selectedDevice: Device | null = null;
  modalTitle = 'Device Settings';
  deviceSettings: DeviceSettings[] = [];
  selectedSetting: DeviceSettings | null = null;
  isLoadingSettings = false;
  fileToUpload: File | null = null;
  defaultSettings: any[] = []; // Store default settings from API
  selectedDefaultSetting: any = null; // Currently selected default setting
  private modalInstance: bootstrap.Modal | null = null;
  // Define table columns
  listOfColumns: ColumnItem[] = [
    { name: 'ID', key: 'id', sortOrder: null, sortFn: (a: Device, b: Device) => a.id - b.id, sortDirections: ['ascend', 'descend', null], width: '80px' },
    { name: 'Name', key: 'name', sortOrder: null, sortFn: (a: Device, b: Device) => a.name.localeCompare(b.name), sortDirections: ['ascend', 'descend', null] },
    { name: 'Device ID', key: 'deviceId', sortOrder: null, sortFn: (a: Device, b: Device) => a.deviceId.localeCompare(b.deviceId), sortDirections: ['ascend', 'descend', null] },
    {
      name: 'Status', key: 'status', sortOrder: null, sortFn: (a: Device, b: Device) => a.status.localeCompare(b.status), sortDirections: ['ascend', 'descend', null],
      filterMultiple: true,
      listOfFilter: [ // Customize based on your actual statuses
        { text: 'Active', value: 'active' }, { text: 'Inactive', value: 'inactive' }, { text: 'Error', value: 'error' }, { text: 'Pending', value: 'pending' }
      ],
      filterFn: (list: any[], item: Device) => list.some(status => item.status.indexOf(status) !== -1)
    },
    { name: 'Created At', key: 'createdAt', sortOrder: 'descend', sortFn: (a: Device, b: Device) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(), sortDirections: ['ascend', 'descend', null] },
    { name: 'Updated At', key: 'updatedAt', sortOrder: null, sortFn: (a: Device, b: Device) => new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime(), sortDirections: ['ascend', 'descend', null] },
    { name: 'Actions', key: 'actions', sortOrder: null, sortFn: null, sortDirections: [], width: '150px' } // Added width for actions
  ];

  constructor(
    private devicesService: DevicesService,
    private cdr: ChangeDetectorRef, // Inject ChangeDetectorRef for OnPush
    public datePipe: DatePipe, // Inject DatePipe (available via SharedModule providers)
    private message: NzMessageService // For showing success/error messages
  ) { }

  ngOnInit(): void {
    this.loadDevices();
    this.loadDeviceSettings();
    this.loadDefaultSettings();
  }

  // Load default settings from API
  loadDefaultSettings(): void {
    this.devicesService.getDefaultSettings()
      .subscribe({
        next: (settings) => {
          this.defaultSettings = settings;
          this.cdr.markForCheck();
        },
        error: (err) => {
          console.error('Error loading default settings:', err);
          this.message.error('Failed to load default settings');
        }
      });
  }

  trackById(index: number, item: Device): number {
    return item.id; // Use the unique identifier of your device object
  }
  onFilterChange(event: any, columnKey: keyof Device | 'actions'): void {
    console.log(`Filter changed for column ${String(columnKey)}:`, event);
    // Add logic here if you need to react to the filter change event itself.
    // For client-side filtering defined via filterFn in listOfColumns,
    // nz-table handles the filtering automatically.
  }
  openModal(device: Device) {
    this.selectedDevice = device;
    this.isLoadingSettings = true;

    // Load device settings for this specific device (using device.id, not deviceId)
    this.devicesService.getDeviceSettingsByDeviceId(device.id)
      .pipe(finalize(() => {
        this.isLoadingSettings = false;
        this.cdr.markForCheck();
      }))
      .subscribe({
        next: (settings) => {
          this.deviceSettings = settings;

          // If there are settings, select the first one by default
          if (settings && settings.length > 0) {
            this.selectedSetting = settings[0];
          } else {
            // Initialize a new setting if none exists
            this.initNewSetting();
          }

          // Open the modal
          const modalElement = document.getElementById('deviceSettings')!;
          this.modalInstance = new bootstrap.Modal(modalElement);
          this.modalInstance.show();
        },
        error: (err) => {
          console.error('Error loading device settings:', err);
          this.message.error('Failed to load device settings');
        }
      });
  }

  closeModal() {
    if (this.modalInstance) {
      this.modalInstance.hide();
    }
  }
  loadDevices(): void {
    this.isLoading = true;
    this.devicesService.getDevices()
      .pipe(
        finalize(() => {
          this.isLoading = false;
          this.cdr.markForCheck(); // Trigger change detection when loading finishes
        })
      )
      .subscribe({
        next: (data) => {
          this.devices = data;

          // Optional: Dynamically populate filters if needed (example for name)
          // this.updateNameFilterOptions(data);
        },
        error: (err) => {
          console.error("Error loading devices:", err);
          this.devices = []; // Clear data on error
          // Add user-friendly error handling (e.g., show a notification)
        }
      });
  }

  // Helper to get badge type from status
  getStatusType(status: Device['status']): 'success' | 'error' | 'processing' | 'warning' | 'default' {
    switch (status?.toLowerCase()) { // Add null check and lower case comparison
      case 'active': return 'success';
      case 'inactive': return 'default';
      case 'error': return 'error';
      case 'pending': return 'processing';
      default: return 'warning'; // Default to warning for unknown statuses
    }
  }

  // --- Action Handlers ---
  editDevice(device: Device): void {
    console.log('Edit:', device.id);
    // TODO: Implement actual edit logic (e.g., navigate to edit page, open modal)
  }

  deleteDevice(device: Device): void {
    console.log('Delete:', device.id);
    // TODO: Implement actual delete logic (e.g., show confirmation modal, call delete API)
    // Example optimistic update (remove from list immediately)
    // this.devices = this.devices.filter(d => d.id !== device.id);
    // this.cdr.markForCheck();
  }

  viewDevice(device: Device): void {
    console.log('View:', device.id);
    // TODO: Implement actual view logic (e.g., navigate to detail page, open modal)
  }

  // --- Optional: Helper to dynamically create filters ---
  // updateNameFilterOptions(data: Device[]): void {
  //   const nameColumn = this.listOfColumns.find(c => c.key === 'name');
  //   if (nameColumn) {
  //     const uniqueNames = [...new Set(data.map(item => item.name))]; // Get unique names
  //     nameColumn.listOfFilter = uniqueNames.map(name => ({ text: name, value: name }));
  //     nameColumn.filterFn = (list: string[], item: Device) => list.some(name => item.name.includes(name));
  //   }
  // }

  // Load all device settings
  loadDeviceSettings(): void {
    this.isLoadingSettings = true;
    this.devicesService.getDeviceSettings()
      .pipe(finalize(() => {
        this.isLoadingSettings = false;
        this.cdr.markForCheck();
      }))
      .subscribe({
        next: (settings) => {
          this.deviceSettings = settings;
        },
        error: (err) => {
          console.error('Error loading device settings:', err);
          this.message.error('Failed to load device settings');
        }
      });
  }

  // Handle file selection
  onFileSelected(event: Event): void {
    const element = event.target as HTMLInputElement;
    if (element.files && element.files.length > 0) {
      this.fileToUpload = element.files[0];
    }
  }

  // Select a setting
  selectSetting(setting: DeviceSettings): void {
    this.selectedSetting = setting;

    // Find and select the corresponding default setting
    if (setting && setting.default_Id && this.defaultSettings) {
      const defaultSetting = this.defaultSettings.find(ds => ds.id === setting.default_Id);
      if (defaultSetting) {
        this.selectedDefaultSetting = defaultSetting;
      }
    }

    this.cdr.markForCheck();
  }

  // Initialize a new setting
  initNewSetting(): void {
    this.selectedSetting = {
      device: this.selectedDevice?.id.toString() || ''
    };

    // Set default setting to the first one if available
    if (this.defaultSettings && this.defaultSettings.length > 0) {
      this.selectedDefaultSetting = this.defaultSettings[0];
      this.selectedSetting.default_Id = this.selectedDefaultSetting.id;
    }

    this.cdr.markForCheck();
  }

  // Handle default setting selection
  onDefaultSettingChange(setting: any): void {
    this.selectedDefaultSetting = setting;

    if (this.selectedSetting) {
      this.selectedSetting.default_Id = setting.id;
    }

    this.cdr.markForCheck();
  }

  // Get default setting name by ID
  getDefaultSettingName(defaultId: number): string {
    if (!defaultId || !this.defaultSettings || this.defaultSettings.length === 0) {
      return 'Unknown Setting';
    }

    const defaultSetting = this.defaultSettings.find(ds => ds.id === defaultId);
    return defaultSetting ? defaultSetting.name : 'Unknown Setting';
  }

  // Save device settings
  saveDeviceSettings(): void {
    if (!this.selectedDevice) {
      this.message.error('No device selected');
      return;
    }

    if (!this.selectedDefaultSetting) {
      this.message.error('Please select a setting type (Animation or Video)');
      return;
    }

    // Check if file is required for video type but not provided
    const isVideoType = this.selectedDefaultSetting.type === 'video';
    if (isVideoType && !this.fileToUpload && (!this.selectedSetting || !this.selectedSetting.filePath)) {
      this.message.error('Please upload a video file');
      return;
    }

    // If a setting is selected, update it
    if (this.selectedSetting && this.selectedSetting.id) {
      const updatedSetting: DeviceSettings = {
        ...this.selectedSetting,
        device: this.selectedDevice.id.toString(),
        default_Id: this.selectedDefaultSetting.id
      };

      this.devicesService.updateDeviceSettings(this.selectedSetting.id, updatedSetting, this.fileToUpload || undefined)
        .subscribe({
          next: () => {
            this.message.success('Device settings updated successfully');
            this.loadDeviceSettings();
            this.closeModal();
          },
          error: (err) => {
            console.error('Error updating device settings:', err);
            this.message.error('Failed to update device settings');
          }
        });
    }
    // Otherwise create a new setting
    else if (this.selectedSetting) {
      const newSetting: DeviceSettings = {
        ...this.selectedSetting,
        device: this.selectedDevice.id.toString(),
        default_Id: this.selectedDefaultSetting.id
      };

      this.devicesService.createDeviceSettings(newSetting, this.fileToUpload || undefined)
        .subscribe({
          next: () => {
            this.message.success('Device settings created successfully');
            this.loadDeviceSettings();
            this.closeModal();
          },
          error: (err) => {
            console.error('Error creating device settings:', err);
            this.message.error('Failed to create device settings');
          }
        });
    } else {
      this.message.error('No settings selected');
    }
  }

  // Delete device settings
  deleteDeviceSettings(setting: DeviceSettings): void {
    if (!setting || !setting.id) {
      this.message.error('No valid setting selected for deletion');
      return;
    }

    this.devicesService.deleteDeviceSettings(setting.id)
      .subscribe({
        next: () => {
          this.message.success('Device settings deleted successfully');
          this.deviceSettings = this.deviceSettings.filter(s => s.id !== setting.id);

          // If the deleted setting was selected, clear the selection
          if (this.selectedSetting && this.selectedSetting.id === setting.id) {
            this.selectedSetting = null;
          }

          this.cdr.markForCheck();
        },
        error: (err) => {
          console.error('Error deleting device settings:', err);
          this.message.error('Failed to delete device settings');
        }
      });
  }
}
