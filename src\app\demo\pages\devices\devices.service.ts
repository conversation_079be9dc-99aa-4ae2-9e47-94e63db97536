// src/app/services/devices.service.ts (or your path)

import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators'; // Make sure map is imported
import { StorageService } from 'src/app/theme/shared/service/storage.service'; // Adjust path if needed
import { environment } from 'src/environments/environment';
import { Device } from './device.model'; // Adjust path to your Device model
import { DeviceSettings } from './device-settings.model';

// Interface for the raw API device data (before status mapping)
interface ApiDevice {
  id: number;
  name: string;
  deviceId: string;
  status: number; // Status is a number from the API
  createdAt: string; // Dates are strings from the API
  updatedAt: string;
}

// Interface describing the overall API response structure
interface ApiResponse<T> {
  status: number;
  message: string;
  success: boolean;
  data: T; // The actual data payload
}

@Injectable({
  providedIn: 'root'
})
export class DevicesService {

  private apiUrl = environment.URL;

  constructor(private http: HttpClient, private storageService: StorageService) { }

  private getHttpOptions() {
    const token = this.storageService.getToken();
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      ...(token ? { Authorization: `Bearer ${token}` } : {})
    });
    return { headers };
  }

  // Helper function to map numeric status to string status
  private mapNumericStatusToString(numericStatus: number): Device['status'] {
    switch (numericStatus) {
      case 1: return 'active';
      case 0: return 'inactive'; // Assuming 0 is inactive
      // Add more cases based on your backend logic
      // case 2: return 'error';
      // case 3: return 'pending';
      default: return 'unknown'; // Handle unexpected statuses
    }
  }

  getDevices(): Observable<Device[]> {
    // Use the ApiResponse interface with the ApiDevice array type for the HTTP call
    return this.http.get<ApiResponse<ApiDevice[]>>(`${this.apiUrl}/devices`, this.getHttpOptions()).pipe(
      map(response => {
        // 1. Access the array inside response.data
        // 2. Provide a fallback empty array if response or response.data is null/undefined
        const apiDevices = response?.data || [];

        // 3. Map over the apiDevices array
        return apiDevices.map(apiDevice => ({
          // Spread the properties from the raw API device
          ...apiDevice,
          // Convert dates from string to Date objects
          createdAt: apiDevice.createdAt ? new Date(apiDevice.createdAt) : null,
          updatedAt: apiDevice.updatedAt ? new Date(apiDevice.updatedAt) : null,
          // Map the numeric status to the string status expected by the component/Device model
          status: this.mapNumericStatusToString(apiDevice.status)
        }));
      })
      // Optional: Add error handling
      // catchError(error => {
      //   console.error('Error fetching devices:', error);
      //   return throwError(() => new Error('Could not load devices.'));
      // })
    );
  }

    // Get all device settings
  getDeviceSettings(): Observable<DeviceSettings[]> {
    return this.http.get<ApiResponse<DeviceSettings[]>>(`${this.apiUrl}/device-settings`, this.getHttpOptions())
      .pipe(map(response => response?.data || []));
  }

  // Get device settings by ID
  getDeviceSettingsById(id: string | number): Observable<DeviceSettings> {
    return this.http.get<ApiResponse<DeviceSettings>>(`${this.apiUrl}/device-settings/${id}`, this.getHttpOptions())
      .pipe(map(response => response?.data));
  }

  // Get device settings by device ID (using the device.id, not deviceId)
  getDeviceSettingsByDeviceId(id: string | number): Observable<DeviceSettings[]> {
    return this.http.get<ApiResponse<DeviceSettings[]>>(`${this.apiUrl}/device-settings/device/${id}`, this.getHttpOptions())
      .pipe(map(response => response?.data || []));
  }

  // Get device settings by user ID (current user if userId is not provided)
  getDeviceSettingsByUserId(userId?: string): Observable<DeviceSettings[]> {
    const url = userId
      ? `${this.apiUrl}/device-settings/user/${userId}`
      : `${this.apiUrl}/device-settings/user`;

    return this.http.get<ApiResponse<DeviceSettings[]>>(url, this.getHttpOptions())
      .pipe(map(response => response?.data || []));
  }

  // Create device settings
  createDeviceSettings(settings: DeviceSettings, file?: File): Observable<DeviceSettings> {
    const formData = new FormData();

    // Add all settings properties to formData
    Object.keys(settings).forEach(key => {
      if (settings[key] !== undefined && settings[key] !== null && key !== 'file') {
        formData.append(key, settings[key]);
      }
    });

    // Add file if provided
    if (file) {
      formData.append('file', file);
    }

    return this.http.post<ApiResponse<DeviceSettings>>(
      `${this.apiUrl}/device-settings`,
      formData,
      {
        headers: new HttpHeaders({
          Authorization: `Bearer ${this.storageService.getToken()}`
          // Don't set Content-Type here, it will be set automatically for FormData
        })
      }
    ).pipe(map(response => response?.data));
  }

  // Update device settings
  updateDeviceSettings(id: string | number, settings: DeviceSettings, file?: File): Observable<DeviceSettings> {
    const formData = new FormData();

    // Add all settings properties to formData
    Object.keys(settings).forEach(key => {
      if (settings[key] !== undefined && settings[key] !== null && key !== 'file') {
        formData.append(key, settings[key]);
      }
    });

    // Add file if provided
    if (file) {
      formData.append('file', file);
    }

    return this.http.put<ApiResponse<DeviceSettings>>(
      `${this.apiUrl}/device-settings/${id}`,
      formData,
      {
        headers: new HttpHeaders({
          Authorization: `Bearer ${this.storageService.getToken()}`
          // Don't set Content-Type here, it will be set automatically for FormData
        })
      }
    ).pipe(map(response => response?.data));
  }

  // Delete device settings
  deleteDeviceSettings(id: string | number): Observable<any> {
    return this.http.delete<ApiResponse<any>>(
      `${this.apiUrl}/device-settings/${id}`,
      this.getHttpOptions()
    ).pipe(map(response => response?.data));
  }

  // Get default settings options (Animation, Video, etc.)
  getDefaultSettings(): Observable<any[]> {
    return this.http.get<ApiResponse<any[]>>(`${this.apiUrl}/default/settings`, this.getHttpOptions())
      .pipe(map(response => response?.data || []));
  }

  // Legacy methods - can be updated or removed if not needed
  getDefault(): Observable<any[]> {
    return this.http.get<ApiResponse<any[]>>(`${this.apiUrl}/device-settings`, this.getHttpOptions())
      .pipe(map(response => response?.data || []));
  }

  setDefaultSettings(): Observable<any[]> {
    return this.http.get<ApiResponse<any[]>>(`${this.apiUrl}/device-settings`, this.getHttpOptions())
      .pipe(map(response => response?.data || []));
  }
}
