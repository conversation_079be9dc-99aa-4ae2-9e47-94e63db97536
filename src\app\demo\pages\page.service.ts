import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class PageService {

  constructor(private http:HttpClient) { }

 GetPosting(token){
    const httpOptions = {
      headers: new HttpHeaders({
        Authorization: `Bearer ${token}`,
      }),
    }
    return this.http.get<any>(
      environment.URL + `/postingByUserId`,
      {
        headers: httpOptions.headers,
      }
    );
   }

   GetDevices(token){
    const httpOptions = {
      headers: new HttpHeaders({
        Authorization: `Bearer ${token}`,
      }),
    }
    return this.http.get<any>(
      environment.URL + `/devices`,
      {
        headers: httpOptions.headers,
      }
    );
   }

   CreatePosting(data:any,token:String){
    const httpOptions = {
      headers: new HttpHeaders({
        Authorization: `Bearer ${token}`,
      }),
    }
    return this.http.post<any>(
      environment.URL + `/posting`,
      data,
      {
        headers: httpOptions.headers,
      }
    );
   }

   UpdatePosting(id: number, data: any, token: string) {
    const httpOptions = {
      headers: new HttpHeaders({
        Authorization: `Bearer ${token}`,
      }),
    }
    return this.http.put<any>(
      environment.URL + `/posting/${id}`,
      data,
      {
        headers: httpOptions.headers,
      }
    );
   }

   DeletePosting(id: number, token: string) {
    const httpOptions = {
      headers: new HttpHeaders({
        Authorization: `Bearer ${token}`,
      }),
    }
    return this.http.delete<any>(
      environment.URL + `/posting/${id}`,
      {
        headers: httpOptions.headers,
      }
    );
   }

   postToScreen(id: number, token: string) {
    const httpOptions = {
      headers: new HttpHeaders({
        Authorization: `Bearer ${token}`,
      }),
    }
    return this.http.put<any>(
      environment.URL + `/posting/${id}/post`,
      {},
      {
        headers: httpOptions.headers,
      }
    );
   }

   unpostToScreen(id: number, token: string) {
    const httpOptions = {
      headers: new HttpHeaders({
        Authorization: `Bearer ${token}`,
      }),
    }
    return this.http.put<any>(
      environment.URL + `/posting/${id}/unpost`,
      {},
      {
        headers: httpOptions.headers,
      }
    );
   }

   toEthiopian(year:number,month:number,day:number,token){
    const httpOptions = {
      headers: new HttpHeaders({
        Authorization: `Bearer ${token}`,
      }),
    }
    return this.http.get<any>(
      environment.URL + `/toEthiopian/${year}/${month}/${day}`,
      {
        headers: httpOptions.headers,
      }
    );
   }
}
