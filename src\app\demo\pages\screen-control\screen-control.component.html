<app-validate-permission  [pageId]="'screen-control'" (permissionChecked)="isPageAllowed = $event"></app-validate-permission>
<div *ngIf="isPageAllowed" class="screen-control-container">
  <div class="action-header">
    <h2 class="page-title">Screen Postings</h2>
    <button (click)="openCreate()" class="add-button">
      <span class="button-icon"><i class="feather icon-plus"></i></span>
      <span class="button-text">Add New Posting</span>
    </button>
  </div>

  <!-- Search and Sort Controls -->
  <div class="controls-container">
    <div class="search-container">
      <nz-input-group [nzSuffix]="suffixIconSearch">
        <input type="text" nz-input [(ngModel)]="searchText" placeholder="Search postings..." (input)="onSearch()" />
      </nz-input-group>
      <ng-template #suffixIconSearch>
        <i nz-icon nzType="search"></i>
      </ng-template>
    </div>

    <div class="sort-container">
      <span class="sort-label">Sort by:</span>
      <nz-button-group>
        <button nz-button
                [nzType]="sortField === 'status' ? 'primary' : 'default'"
                (click)="onSort('status')">
          Status
         
        </button>
        <button nz-button
                [nzType]="sortField === 'name' ? 'primary' : 'default'"
                (click)="onSort('name')">
          Name

        </button>
        <button nz-button
                [nzType]="sortField === 'date' ? 'primary' : 'default'"
                (click)="onSort('date')">
          Date
          <i nz-icon
             [nzType]="sortField === 'date' ? (sortOrder === 'ascend' ? 'sort-ascending' : 'sort-descending') : 'swap'"
             class="sort-icon"></i>
        </button>
        <button nz-button
                [nzType]="sortField === 'device' ? 'primary' : 'default'"
                (click)="onSort('device')">
          Device
          <i nz-icon
             [nzType]="sortField === 'device' ? (sortOrder === 'ascend' ? 'sort-ascending' : 'sort-descending') : 'swap'"
             class="sort-icon"></i>
        </button>
      </nz-button-group>

      <button nz-button
              nzType="default"
              (click)="sortOrder = sortOrder === 'ascend' ? 'descend' : 'ascend'; updateDisplayData()">
        <i nz-icon [nzType]="sortOrder === 'ascend' ? 'up' : 'down'"></i>
        {{ sortOrder === 'ascend' ? 'Ascending' : 'Descending' }}
      </button>
    </div>
  </div>
  <div *ngIf="listOfData.length > 0 && !loading" class="row mt-3">
    <!-- Loop through the paginated data -->
    @for (data of displayData; track data.id) {
    <div class="sm-card"
         [ngClass]="{
           'status-online': data.status === 1,
           'status-draft': data.status === 0,
           'status-scheduled': data.isScheduled === 1,
           'status-error': data.status === 3
         }">
      <!-- Animated floating effect for scheduled items -->
      <div *ngIf="data.isScheduled === 1" class="scheduled-badge" title="Scheduled posting">
        <i class="feather icon-clock"></i>
      </div>
      <!-- Header -->
      <div class="header">
        <div class="profile">
          <div class="profile-img">
            <i *ngIf="data.fileType === 'image'" class="feather icon-image"></i>
            <i *ngIf="data.fileType === 'video'" class="feather icon-video"></i>
            <i *ngIf="data.fileType !== 'image' && data.fileType !== 'video'" class="feather icon-file"></i>
          </div>
          <div class="profile-info">
            <div class="name" [title]="getFileName(data.filePath)">{{ getFileName(data.filePath) }}</div>
            <div class="phone">{{ data.device?.name }}</div>
          </div>
        </div>
        <!-- Status (Uses updated filterStatus) -->
        <div class="status" [ngStyle]="{color: filterStatus(data.status, data.isScheduled)?.color || 'black'}">
          {{ filterStatus(data.status,data.isScheduled)?.name || 'Unknown' }}
        </div>
      </div>

      <!-- Content Section with Mini Preview -->
      <div class="content">
        <!-- Mini Preview Area -->
        <div class="mini-preview" *ngIf="data.filePath">
          <!-- Image Preview -->

          <img *ngIf="data.fileType === 'image'"
               [src]="baseApiUrl + data.filePath"
               [alt]="'Preview of ' + getFileName(data.filePath)"
               class="preview-thumbnail"
               (error)="onImageError($event)"> <!-- Handle load errors -->

          <!-- Video Preview with Play Button -->
          <div *ngIf="data.fileType === 'video'" class="mini-preview video-preview"
               [ngClass]="{'playing': currentlyPlayingId === data.id && isVideoPlaying}">
            <video #videoElement [src]="baseApiUrl + data.filePath"
                  class="preview-thumbnail"
                  preload="metadata"
                  (click)="toggleVideoPlayback(data, $event, videoElement)"
                  (error)="onVideoError($event)">
            </video>
            <div class="video-controls" (click)="toggleVideoPlayback(data, $event, videoElement)">
              <div class="play-button" *ngIf="currentlyPlayingId !== data.id || !isVideoPlaying">
                <i class="feather icon-play"></i>
              </div>
              <div class="pause-button" *ngIf="currentlyPlayingId === data.id && isVideoPlaying">
                <i class="feather icon-pause"></i>
              </div>
            </div>
          </div>

          <!-- Other File Placeholder -->
          <div *ngIf="data.fileType !== 'image' && data.fileType !== 'video'" class="preview-placeholder preview-other">
            <i class="feather icon-file-text"></i>
          </div>

          <!-- Fallback/Error Placeholder (shown by onImageError/onVideoError via class) -->

        </div>

        <!-- Other Content Details -->
        <div class="priority">
          <span class="icon">🚦</span>
          <div class="value">{{ data.priority }}</div>
        </div>
        <div class="datetime">
          <span class="icon">⏰</span>
          <div *ngIf="data.isScheduled" class="value" [title]="(data.startDate | date:'medium') + ' - ' + (data.endDate | date:'medium')">
            {{ data.startDate | date: 'dd-MM-yyyy'  }} - {{ data.endDate | date: 'dd-MM-yyyy' }}
          </div>
          <div *ngIf="!data.isScheduled" class="value" [title]="data.createdAt | date:'medium'">
            Added: {{ data.createdAt | date:'short' }}
          </div>
        </div>
        <div class="file-info">
           <span class="icon">📁</span>
           <div class="value">{{ data.fileType }}</div>
        </div>
      </div>

      <!-- Footer with Actions -->
      <div class="footer">
        <!-- Edit Button -->
        <button *ngIf="data.status === 0" nz-button nzType="default" nzShape="circle" (click)="openUpdate(data, $event)"
          nz-popover nzPopoverTitle="Edit Details" nzPopoverPlacement="top">
          <i class="feather icon-edit text-warning"></i>
        </button>

        <!-- Delete Button with Confirmation -->
        <button nz-button nzType="default" nzShape="circle"
         [hidden]="data.status === 1"
         nz-popconfirm
         nzPopconfirmTitle="Are you sure you want to delete this posting?"
         nzPopconfirmPlacement="top"
         (nzOnConfirm)="deleteItem(data.id)">
          <i class="feather icon-trash text-danger"></i>
        </button>

        <!-- Info/Details Button -->
        <button *ngIf="data.status === 0" nz-button nzType="default" nzShape="circle" (click)="postToScreen(data.id, $event)"
         nz-popover nzPopoverTitle="View Details" nzPopoverPlacement="top">
          <i class="feather icon-cloud text-info"></i>
        </button>
        <button *ngIf="data.status === 1" nz-button nzType="default" nzShape="circle" (click)="unpostToScreen(data.id, $event)"
        nz-popover nzPopoverTitle="View Details" nzPopoverPlacement="top">
         <i class="feather icon-cloud-off text-danger"></i>
       </button>

        <!-- Full Preview Button -->
         <button *ngIf="data.filePath" nz-button nzType="default" nzShape="circle" (click)="previewFile(data, $event)"
          nz-popover nzPopoverTitle="Preview Full" nzPopoverPlacement="top">
           <i class="feather icon-eye text-primary"></i>
         </button>

      </div>
    </div>
    }
  </div>

  <!-- Pagination -->
  <div *ngIf="listOfData.length > 0 && totalItems > pageSize" class="pagination-container">
    <nz-pagination
      [nzPageIndex]="currentPage"
      [nzPageSize]="pageSize"
      [nzTotal]="totalItems"
      [nzShowSizeChanger]="true"
      [nzPageSizeOptions]="[9, 18, 36]"
      (nzPageIndexChange)="onPageChange($event)"
      (nzPageSizeChange)="onPageSizeChange($event)">
    </nz-pagination>
  </div>

  <!-- Enhanced message for no data -->
   <div *ngIf="listOfData.length === 0 && !loading" class="no-data-message">
     <div class="empty-state">
       <i class="feather icon-inbox empty-icon"></i>
       <h3>No Screen Postings Found</h3>
       <p>Create your first posting by clicking the Add button above.</p>
       <button (click)="openCreate()" class="btn btn-primary empty-action">
         <i class="feather icon-plus-circle"></i> Create New Posting
       </button>
     </div>
   </div>

   <!-- Enhanced loading indicator -->
   <div *ngIf="loading" class="loading-spinner">
     <nz-spin nzSimple [nzSize]="'large'"></nz-spin>
     <p class="loading-text">Loading screen postings...</p>
   </div>
</div>

<nz-drawer
  [nzClosable]="true"
  [nzVisible]="showUpdate"
  nzPlacement="right"
  nzTitle="Update Screen Posting"
  (nzOnClose)="closeUpdate()"
  class="drawer-container"
  [nzWidth]="480"
>
  <ng-container *nzDrawerContent>
    <div class="drawer-content">
      <div class="modern-form">
        <!-- Device and Priority Section -->
        <div class="form-section">
          <h4>Screen Details</h4>
          <mat-form-field>
            <mat-label>Select Screen</mat-label>
            <mat-select [(ngModel)]="deviceModel" name="device">
              @for (device of listOfDevices; track device) {
                <mat-option [value]="device.id">{{device.name}}</mat-option>
              }
            </mat-select>
            <mat-error *ngIf="errors.deviceId">{{errors.deviceId}}</mat-error>
          </mat-form-field>

          <mat-form-field>
            <mat-label>Select Priority</mat-label>
            <mat-select [(ngModel)]="priorityModel" name="priority">
              @for (pr of priorityLabel; track pr) {
                <mat-option [value]="pr.name">{{pr.name}}</mat-option>
              }
            </mat-select>
            <mat-error *ngIf="errors.priority">{{errors.priority}}</mat-error>
          </mat-form-field>
        </div>

        <!-- Schedule Section -->
        <div class="form-section">
          <h4>Schedule Settings</h4>
          <div class="schedule-toggle">
            <mat-checkbox [(ngModel)]="IsScheduledModel">
              <span class="checkbox-label">Schedule this posting</span>
            </mat-checkbox>
          </div>

          <div class="schedule-fields" *ngIf="IsScheduledModel">
            <div class="date-time-group">
              <h5>Start Date</h5>
              <div class="date-time-row">
                <div style="display: flex; flex-direction: column">
                  <mat-form-field>
                    <mat-label>Day</mat-label>
                    <mat-select name="changeChannel" [(ngModel)]="dayStartDateEditModel" >
                      @for (item of days; track $index) {
                        <mat-option [value]="item">{{ item }}</mat-option>
                      }
                    </mat-select>
                  </mat-form-field>
                  <mat-form-field>
                    <mat-label>Month</mat-label>
                    <mat-select name="changeChannel" [(ngModel)]="monthStartDateEditModel" >
                      @for (item of months; track $index) {
                        <mat-option [value]="item.id">{{ item.name }}</mat-option>
                      }
                    </mat-select>
                  </mat-form-field>

                  <mat-form-field>
                    <mat-label>Year</mat-label>
                    <mat-select name="changeChannel" [(ngModel)]="yearStartDateEditModel">
                      @for (item of years; track $index) {
                        <mat-option [value]="item">{{ item }}</mat-option>
                      }
                    </mat-select>
                  </mat-form-field>
                  <!-- <div class="time-field">
                    <label>Time <span class="required-mark">*</span></label>
                    <nz-time-picker
                      [nzUse12Hours]="true"
                      [nzPlaceHolder]="'Select time'"
                      [(ngModel)]="startTimeModel"
                      (ngModelChange)="onChange($event)"
                      [nzFormat]="'hh:mm a'"
                    ></nz-time-picker>
                  </div> -->
                </div>


              </div>
            </div>

            <div class="date-time-group">
              <h5>End Date</h5>
              <div class="date-time-row">
                <div style="display: flex; flex-direction: column">
                  <mat-form-field>
                    <mat-label>Day</mat-label>
                    <mat-select name="changeChannel" [(ngModel)]="dayEndDateEditModel" >
                      @for (item of days; track $index) {
                        <mat-option [value]="item">{{ item }}</mat-option>
                      }
                    </mat-select>
                  </mat-form-field>
                  <mat-form-field>
                    <mat-label>Month</mat-label>
                    <mat-select name="changeChannel" [(ngModel)]="monthEndDateEditModel" >
                      @for (item of months; track $index) {
                        <mat-option [value]="item.id">{{ item.name }}</mat-option>
                      }
                    </mat-select>
                  </mat-form-field>

                  <mat-form-field>
                    <mat-label>Year</mat-label>
                    <mat-select name="changeChannel" [(ngModel)]="yearEndDateEditModel">
                      @for (item of years; track $index) {
                        <mat-option [value]="item">{{ item }}</mat-option>
                      }
                    </mat-select>
                  </mat-form-field>
                  <!-- <div class="time-field">
                    <label>Time <span class="required-mark">*</span></label>
                    <nz-time-picker
                      [nzUse12Hours]="true"
                      [nzPlaceHolder]="'Select time'"
                      [(ngModel)]="endTimeModel"
                      (ngModelChange)="onChange($event)"
                      [nzFormat]="'hh:mm a'"
                    ></nz-time-picker>
                  </div> -->
                </div>
                <!-- <div class="date-field">
                  <label>Date <span class="required-mark">*</span></label>
                  <nz-date-picker
                    [nzPlaceHolder]="'Select date'"
                    [(ngModel)]="endDateModel"
                    (ngModelChange)="onChange($event)"
                    [nzFormat]="'MM/dd/yyyy'"
                  ></nz-date-picker>
                </div>
                <div class="time-field">
                  <label>Time <span class="required-mark">*</span></label>
                  <nz-time-picker
                    [nzUse12Hours]="true"
                    [nzPlaceHolder]="'Select time'"
                    [(ngModel)]="endTimeModel"
                    (ngModelChange)="onChange($event)"
                    [nzFormat]="'hh:mm a'"
                  ></nz-time-picker>
                </div> -->
              </div>
              <mat-error *ngIf="errors.numberOfDays">{{errors.numberOfDays}}</mat-error>
            </div>
          </div>
        </div>

        <!-- File Upload Section -->
        <div class="form-section">
          <h4>Content File</h4>
          <div *ngIf="fileName" class="current-file">
            <p><strong>Current File:</strong> {{ fileName }}</p>
          </div>
          <div class="file-upload-container">
            <label>Select File <span class="optional-mark" *ngIf="editMode">(Optional - only if you want to change the file)</span></label>
            <nz-upload
              [nzShowUploadList]="false"
              [nzMultiple]="false"
              [nzHeaders]="{ authorization: 'authorization-text' }"
              (nzChange)="handleChange($event)"
            >
              <button nz-button [nzType]="'primary'">
                <span nz-icon nzType="upload"></span>
                <span *ngIf="!selectedFile">Select File</span>
                <span *ngIf="selectedFile">
                  {{ selectedFile.length > 20 ? (selectedFile | slice:0:20) + '...' : selectedFile }}
                </span>
              </button>
            </nz-upload>
            <mat-error *ngIf="errors.selectedFile">{{errors.selectedFile}}</mat-error>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer with Action Buttons -->
    <div class="drawer-footer">
      <button (click)="closeUpdate()" class="btn btn-dark">
        <i class="feather icon-x"></i>
        <span>Cancel</span>
      </button>
      <button [disabled]="loading" class="btn btn-success" (click)="onUpdateSubmit()">
        <nz-spin *ngIf="loading" nzSimple [nzSize]="'small'"></nz-spin>
        <span *ngIf="!loading">
          <i class="feather icon-save"></i>
          <span>Save Changes</span>
        </span>
      </button>
    </div>
  </ng-container>
</nz-drawer>


<nz-drawer
  [nzClosable]="true"
  [nzVisible]="showAdd"
  nzPlacement="right"
  nzTitle="Add Screen Posting"
  (nzOnClose)="closeCreate()"
  class="drawer-container"
  [nzWidth]="480"
>
  <ng-container *nzDrawerContent>
    <div class="drawer-content">
      <div class="modern-form">
        <!-- Device and Priority Section -->
        <div class="form-section">
          <h4>Screen Details</h4>
          <mat-form-field>
            <mat-label>Select Screen</mat-label>
            <mat-select [(ngModel)]="deviceModel" name="device">
              @for (device of listOfDevices; track device) {
                <mat-option [value]="device.id">{{device.name}}</mat-option>
              }
            </mat-select>
            <mat-error *ngIf="errors.deviceId">{{errors.deviceId}}</mat-error>
          </mat-form-field>

          <mat-form-field>
            <mat-label>Select Priority</mat-label>
            <mat-select [(ngModel)]="priorityModel" name="priority">
              @for (pr of priorityLabel; track pr) {
                <mat-option [value]="pr.name">{{pr.name}}</mat-option>
              }
            </mat-select>
            <mat-error *ngIf="errors.priority">{{errors.priority}}</mat-error>
          </mat-form-field>
        </div>

        <!-- Schedule Section -->
        <div class="form-section">
          <h4>Schedule Settings</h4>
          <div class="schedule-toggle">
            <mat-checkbox [(ngModel)]="IsScheduledModel">
              <span class="checkbox-label">Schedule this posting</span>
            </mat-checkbox>
          </div>

          <div class="schedule-fields" *ngIf="IsScheduledModel">
            <div class="date-time-group">
              <h5>Start Date</h5>
              <div class="date-time-row">
                <div style="display: flex; flex-direction: column">
                  <mat-form-field>
                    <mat-label>Day</mat-label>
                    <mat-select name="changeChannel" [(ngModel)]="dayStartDateModel" >
                      @for (item of days; track $index) {
                        <mat-option [value]="item">{{ item }}</mat-option>
                      }
                    </mat-select>
                  </mat-form-field>
                  <mat-form-field>
                    <mat-label>Month</mat-label>
                    <mat-select name="changeChannel" [(ngModel)]="monthStartDateModel" >
                      @for (item of months; track $index) {
                        <mat-option [value]="item.id">{{ item.name }}</mat-option>
                      }
                    </mat-select>
                  </mat-form-field>

                  <mat-form-field>
                    <mat-label>Year</mat-label>
                    <mat-select name="changeChannel" [(ngModel)]="yearStartDateModel">
                      @for (item of years; track $index) {
                        <mat-option [value]="item">{{ item }}</mat-option>
                      }
                    </mat-select>
                  </mat-form-field>
                  <!-- <div class="time-field">
                    <label>Time <span class="required-mark">*</span></label>
                    <nz-time-picker
                      [nzUse12Hours]="true"
                      [nzPlaceHolder]="'Select time'"
                      [(ngModel)]="startTimeModel"
                      (ngModelChange)="onChange($event)"
                      [nzFormat]="'hh:mm a'"
                    ></nz-time-picker>
                  </div> -->
                </div>
                <!-- <div class="date-field">
                  <label>Date <span class="required-mark">*</span></label>
                  <nz-date-picker
                    [nzPlaceHolder]="'Select date'"
                    [(ngModel)]="startDateModel"
                    (ngModelChange)="onChange($event)"
                    [nzFormat]="'MM/dd/yyyy'"
                  ></nz-date-picker>
                </div>
                <div class="time-field">
                  <label>Time <span class="required-mark">*</span></label>
                  <nz-time-picker
                    [nzUse12Hours]="true"
                    [nzPlaceHolder]="'Select time'"
                    [(ngModel)]="startTimeModel"
                    (ngModelChange)="onChange($event)"
                    [nzFormat]="'hh:mm a'"
                  ></nz-time-picker>
                </div> -->

              </div>
            </div>

            <div class="date-time-group">
              <h5>End Date</h5>
              <div class="date-time-row">
                <div style="display: flex; flex-direction: column">
                  <mat-form-field>
                    <mat-label>Day</mat-label>
                    <mat-select name="changeChannel" [(ngModel)]="dayEndDateModel" >
                      @for (item of days; track $index) {
                        <mat-option [value]="item">{{ item }}</mat-option>
                      }
                    </mat-select>
                  </mat-form-field>
                  <mat-form-field>
                    <mat-label>Month</mat-label>
                    <mat-select name="changeChannel" [(ngModel)]="monthEndDateModel" >
                      @for (item of months; track $index) {
                        <mat-option [value]="item.id">{{ item.name }}</mat-option>
                      }
                    </mat-select>
                  </mat-form-field>

                  <mat-form-field>
                    <mat-label>Year</mat-label>
                    <mat-select name="changeChannel" [(ngModel)]="yearEndDateModel">
                      @for (item of years; track $index) {
                        <mat-option [value]="item">{{ item }}</mat-option>
                      }
                    </mat-select>
                  </mat-form-field>
                  <!-- <div class="time-field">
                    <label>Time <span class="required-mark">*</span></label>
                    <nz-time-picker
                      [nzUse12Hours]="true"
                      [nzPlaceHolder]="'Select time'"
                      [(ngModel)]="endTimeModel"
                      (ngModelChange)="onChange($event)"
                      [nzFormat]="'hh:mm a'"
                    ></nz-time-picker>
                  </div> -->
                </div>
                <!-- <div class="date-field">
                  <label>Date <span class="required-mark">*</span></label>
                  <nz-date-picker
                    [nzPlaceHolder]="'Select date'"
                    [(ngModel)]="endDateModel"
                    (ngModelChange)="onChange($event)"
                    [nzFormat]="'MM/dd/yyyy'"
                  ></nz-date-picker>
                </div>
                <div class="time-field">
                  <label>Time <span class="required-mark">*</span></label>
                  <nz-time-picker
                    [nzUse12Hours]="true"
                    [nzPlaceHolder]="'Select time'"
                    [(ngModel)]="endTimeModel"
                    (ngModelChange)="onChange($event)"
                    [nzFormat]="'hh:mm a'"
                  ></nz-time-picker>
                </div> -->
              </div>
              <mat-error *ngIf="errors.numberOfDays">{{errors.numberOfDays}}</mat-error>
            </div>
          </div>
        </div>

        <!-- File Upload Section -->
        <div class="form-section">
          <h4>Content File</h4>
          <div class="file-upload-container">
            <label>Select File <span class="required-mark">*</span></label>
            <nz-upload
              [nzShowUploadList]="false"
              [nzMultiple]="false"
              [nzHeaders]="{ authorization: 'authorization-text' }"
              (nzChange)="handleChange($event)"
            >
              <button nz-button [nzType]="'primary'">
                <span nz-icon nzType="upload"></span>
                <span *ngIf="!selectedFile">Select File</span>
                <span *ngIf="selectedFile">
                  {{ selectedFile.length > 20 ? (selectedFile | slice:0:20) + '...' : selectedFile }}
                </span>
              </button>
            </nz-upload>
            <mat-error *ngIf="errors.selectedFile">{{errors.selectedFile}}</mat-error>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer with Action Buttons -->
    <div class="drawer-footer">
      <button (click)="closeCreate()" class="btn btn-dark">
        <i class="feather icon-x"></i>
        <span>Cancel</span>
      </button>
      <button [disabled]="loading" class="btn btn-success" (click)="onSubmit()">
        <nz-spin *ngIf="loading" nzSimple [nzSize]="'small'"></nz-spin>
        <span *ngIf="!loading">
          <i class="feather icon-save"></i>
          <span>Create Posting</span>
        </span>
      </button>
    </div>
  </ng-container>
</nz-drawer>
