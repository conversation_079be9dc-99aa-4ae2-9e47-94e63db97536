::ng-deep .card {
  background-color: rgb(23 110 65 / 9%) !important;
}

.screen-control-container {
  padding: 10px;
}

/* Search and Sort Controls */
.controls-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin: 16px 0;
  padding: 16px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.search-container {
  flex: 1;
  min-width: 250px;

  ::ng-deep nz-input-group {
    width: 100%;

    .ant-input-affix-wrapper {
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.7);
      border: 1px solid rgba(24, 144, 255, 0.2);
      transition: all 0.3s ease;

      &:hover, &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }

      .ant-input {
        background: transparent;
      }
    }
  }
}

.sort-container {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;

  .sort-label {
    font-weight: 500;
    color: #555;
    margin-right: 4px;
  }

  ::ng-deep nz-button-group {
    margin-right: 12px;

    .ant-btn {
      border-radius: 0;

      &:first-child {
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
      }

      &:last-child {
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
      }
    }
  }

  .sort-icon {
    margin-left: 4px;
    font-size: 12px;
  }

  ::ng-deep .ant-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    &.ant-btn-primary {
      background: linear-gradient(135deg, #1890ff, #096dd9);
      border-color: #1890ff;

      &:hover {
        background: linear-gradient(135deg, #40a9ff, #1890ff);
      }
    }
  }
}

@media (max-width: 768px) {
  .controls-container {
    flex-direction: column;
  }

  .search-container, .sort-container {
    width: 100%;
  }
}

.action-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.5);

  .page-title {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0;
    position: relative;
    padding-left: 15px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 5px;
      height: 24px;
      background: linear-gradient(to bottom, #1890ff, #096dd9);
      border-radius: 3px;
    }
  }

  .add-button {
    background: linear-gradient(135deg, #52c41a, #389e0d);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(82, 196, 26, 0.5);
      background: linear-gradient(135deg, #73d13d, #52c41a);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 8px rgba(82, 196, 26, 0.3);
    }

    .button-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
    }

    @media (max-width: 480px) {
      padding: 8px 12px;
      font-size: 14px;

      .button-text {
        display: none;
      }

      .button-icon {
        margin-right: 0;
      }
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;

    .add-button {
      align-self: stretch;
      justify-content: center;
    }
  }
}

.sm-card {
  width: 300px;
  border-radius: 16px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.3);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.5);
  margin-left: 0; /* Using gap in parent instead */
  margin-top: 0; /* Using gap in parent instead */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  /* Hover effect */
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);

    /* Subtle glow effect on hover */
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border-radius: 16px;
      box-shadow: 0 0 20px rgba(74, 144, 226, 0.3);
      opacity: 0;
      animation: glow 1.5s ease-in-out infinite alternate;
      pointer-events: none; /* Ensure this doesn't block clicks */
      z-index: 1; /* Lower z-index than buttons */
    }
  }

  /* Status indicator dot in corner */
  &::before {
    content: '';
    position: absolute;
    top: 10px;
    right: 10px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #ccc;
    z-index: 1;
    pointer-events: none; /* Ensure this doesn't block clicks */
  }

  &.status-online::before {
    background-color: #52c41a; /* Green for online */
    box-shadow: 0 0 8px rgba(82, 196, 26, 0.8);
  }

  &.status-draft::before {
    background-color: #faad14; /* Yellow for draft */
    box-shadow: 0 0 8px rgba(250, 173, 20, 0.8);
  }

  &.status-scheduled::before {
    background-color: #1890ff; /* Blue for scheduled */
    box-shadow: 0 0 8px rgba(24, 144, 255, 0.8);
  }

  &.status-error::before {
    background-color: #f5222d; /* Red for error */
    box-shadow: 0 0 8px rgba(245, 34, 45, 0.8);
  }
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
  position: relative;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.profile {
  display: flex;
  align-items: center;
  overflow: hidden;
}

.profile-img {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(74, 144, 226, 0.7), rgba(46, 91, 255, 0.8));
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 22px;
  margin-right: 12px;
  flex-shrink: 0;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
  }
}

/* File type specific icon colors */
.profile-img .icon-image {
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
.profile-img .icon-video {
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
.profile-img .icon-file {
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.profile-info {
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 160px;
  margin-bottom: 4px;
  transition: color 0.3s ease;

  &:hover {
    color: #1890ff;
  }
}

.phone { /* Device name */
  font-size: 13px;
  color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;

  &::before {
    content: '📱';
    margin-right: 4px;
    font-size: 11px;
  }
}

.status {
  background: rgba(255, 255, 255, 0.4);
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(8px);
  white-space: nowrap;
  flex-shrink: 0;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
  }
}

.content {
  background: rgba(255, 255, 255, 0.25);
  padding: 15px;
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  flex-grow: 1;
  margin-bottom: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.35);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  }
}

.content div { /* General style for rows inside content */
  font-size: 14px;
  color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 6px 8px;
  border-radius: 8px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.4);
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.icon {
  margin-right: 10px;
  font-size: 18px;
  width: 24px;
  height: 24px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.value {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Specific content rows with unique styling */
.priority {
  .icon {
    background: rgba(250, 173, 20, 0.2);
    color: #d48806;
  }
}

.datetime {
  .icon {
    background: rgba(24, 144, 255, 0.2);
    color: #0958d9;
  }
}

.file-info {
  .icon {
    background: rgba(82, 196, 26, 0.2);
    color: #389e0d;
  }
}

.footer {
  margin-top: auto;
  display: flex;
  justify-content: space-around;
  padding: 12px 0 0;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  z-index: 10; /* Ensure footer is above other elements */
}

/* Enhanced button styles */
.footer button[nz-button] {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  margin: 0 5px;
  background: rgba(255, 255, 255, 0.6);
  border: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  cursor: pointer; /* Ensure cursor shows as pointer */
  z-index: 20; /* Higher z-index for buttons */
  pointer-events: auto; /* Ensure clicks are captured */

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
    opacity: 0;
    transform: scale(0.5);
    transition: all 0.3s ease;
    pointer-events: none; /* Prevent this element from blocking clicks */
  }

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.15);

    &::before {
      opacity: 0.8;
      transform: scale(2);
    }

    i {
      transform: scale(1.2);
    }
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  i {
    transition: transform 0.3s ease;
    position: relative;
    z-index: 1;
    pointer-events: none; /* Prevent icon from blocking clicks */
  }

  /* Button type-specific styles */
  i.text-warning { /* Edit button */
    color: #faad14;
    text-shadow: 0 1px 2px rgba(250, 173, 20, 0.2);
  }

  i.text-danger { /* Delete button */
    color: #f5222d;
    text-shadow: 0 1px 2px rgba(245, 34, 45, 0.2);
  }

  i.text-info { /* Info button */
    color: #1890ff;
    text-shadow: 0 1px 2px rgba(24, 144, 255, 0.2);
  }

  i.text-primary { /* Preview button */
    color: #722ed1;
    text-shadow: 0 1px 2px rgba(114, 46, 209, 0.2);
  }
}

/* Add keyframe animations */
@keyframes glow {
  from {
    opacity: 0;
  }
  to {
    opacity: 0.5;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Scheduled badge */
.scheduled-badge {
  position: absolute;
  top: -10px;
  right: 20px;
  background: #1890ff;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
  z-index: 5; /* Higher than card but lower than buttons */
  animation: float 3s ease-in-out infinite;
  pointer-events: none; /* Ensure this doesn't block clicks */

  i {
    font-size: 16px;
  }

  &:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 12px rgba(24, 144, 255, 0.6);
  }
}

/* Enhanced styles for loading/no-data states */
.loading-spinner, .no-data-message {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;

  .loading-text {
    margin-top: 20px;
    font-size: 16px;
    color: #1890ff;
    animation: pulse 1.5s infinite;
  }
}

.empty-state {
  background: rgba(255, 255, 255, 0.5);
  border-radius: 16px;
  padding: 40px 20px;
  max-width: 500px;
  margin: 0 auto;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.5);

  .empty-icon {
    font-size: 60px;
    color: #bfbfbf;
    margin-bottom: 20px;
    display: block;
  }

  h3 {
    font-size: 24px;
    color: #333;
    margin-bottom: 10px;
  }

  p {
    font-size: 16px;
    color: #666;
    margin-bottom: 25px;
  }

  .empty-action {
    background: linear-gradient(135deg, #1890ff, #096dd9);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 16px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(24, 144, 255, 0.6);
      background: linear-gradient(135deg, #40a9ff, #1890ff);
    }

    i {
      font-size: 18px;
    }
  }
}

/* Pagination Container Styles */
.pagination-container {
  display: flex;
  justify-content: center;
  margin: 30px 0;
  padding: 15px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.5);

  ::ng-deep {
    .ant-pagination {
      .ant-pagination-item {
        border-radius: 8px;
        border: 1px solid rgba(24, 144, 255, 0.2);
        background: rgba(255, 255, 255, 0.7);
        transition: all 0.3s ease;

        a {
          color: #333;
        }

        &:hover {
          border-color: #1890ff;
          background: rgba(255, 255, 255, 0.9);
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

          a {
            color: #1890ff;
          }
        }

        &.ant-pagination-item-active {
          background: linear-gradient(135deg, #1890ff, #096dd9);
          border-color: #1890ff;
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);

          a {
            color: white;
          }

          &:hover {
            background: linear-gradient(135deg, #40a9ff, #1890ff);
          }
        }
      }

      .ant-pagination-prev, .ant-pagination-next {
        button {
          border-radius: 8px;
          background: rgba(255, 255, 255, 0.7);
          border: 1px solid rgba(24, 144, 255, 0.2);
          transition: all 0.3s ease;

          &:hover {
            border-color: #1890ff;
            background: rgba(255, 255, 255, 0.9);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            color: #1890ff;
          }
        }
      }

      .ant-pagination-options {
        .ant-select-selector {
          border-radius: 8px;
          background: rgba(255, 255, 255, 0.7);
          border: 1px solid rgba(24, 144, 255, 0.2);
          transition: all 0.3s ease;

          &:hover {
            border-color: #1890ff;
            background: rgba(255, 255, 255, 0.9);
          }
        }

        .ant-select-dropdown {
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);

          .ant-select-item {
            transition: all 0.3s ease;

            &:hover {
              background: rgba(24, 144, 255, 0.1);
            }

            &.ant-select-item-option-selected {
              background: rgba(24, 144, 255, 0.2);
              color: #1890ff;
              font-weight: 500;
            }
          }
        }
      }
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* Modern responsive grid layout */
.row {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 25px;
  padding: 10px;
  width: 100%;

  /* Ensure cards maintain their width */
  .sm-card {
    width: 100%;
    margin: 0;
    height: 100%;
    min-height: 350px; /* Ensure consistent height */
    display: flex;
    flex-direction: column;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

// Keep all your existing styles from the previous example

/* --- Enhanced Mini Preview Styles --- */
.mini-preview {
  width: 100%;
  margin-bottom: 15px;
  position: relative;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 10px;
  overflow: hidden;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.02);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);

    &::after {
      opacity: 1;
    }
  }

  /* Overlay gradient on hover */
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0), rgba(0,0,0,0.3));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }

  /* Enhanced video preview styles */
  &.video-preview {
    position: relative;
    overflow: hidden;

    .video-controls {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.3);
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: 5;
      cursor: pointer;

      .play-button, .pause-button {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;

        i {
          font-size: 24px;
          color: #1890ff;
          transition: all 0.3s ease;
        }

        &:hover {
          transform: scale(1.1);
          background: rgba(255, 255, 255, 1);

          i {
            color: #096dd9;
          }
        }
      }
    }

    &:hover .video-controls {
      opacity: 1;
    }

    /* Styles for when video is playing */
    &.playing {
      .video-controls {
        opacity: 0;
        background: rgba(0, 0, 0, 0.1);

        &:hover {
          opacity: 1;
        }
      }

      .preview-thumbnail {
        transform: scale(1.05);
      }
    }
  }
}

.preview-thumbnail {
  display: block;
  width: 100%;
  height: 140px;
  object-fit: cover;
  object-position: center;
  border-radius: 10px;
  transition: transform 0.5s ease;

  &:hover {
    transform: scale(1.05);
  }
}

.preview-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 140px;
  background: linear-gradient(135deg, rgba(240, 240, 240, 0.5), rgba(220, 220, 220, 0.7));
  border-radius: 10px;
  color: #555;
  font-size: 40px;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, rgba(230, 230, 230, 0.6), rgba(210, 210, 210, 0.8));
    color: #333;
  }

  i {
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  }
}

/* Error placeholder styling */
.preview-placeholder.preview-error {
  display: none;
  background: linear-gradient(135deg, rgba(255, 235, 235, 0.7), rgba(255, 200, 200, 0.8));
  color: #dc3545;
  flex-direction: column;
  gap: 10px;

  i {
    font-size: 32px;
    margin-bottom: 5px;
  }

  span {
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    max-width: 80%;
  }
}

/* Show error placeholder when image or video fails */
.mini-preview.has-error .preview-thumbnail {
  display: none;
}
.mini-preview.has-error .preview-placeholder.preview-error {
  display: flex;
}
.mini-preview.has-error .video-controls {
  display: none;
}

/* Optional: Specific styles for video/other placeholders */
.preview-placeholder.preview-video {
   /* color: #007bff; */
}
.preview-placeholder.preview-other {
   /* color: #6c757d; */
}

// --- Other Style improvements ---
.loading-spinner nz-spin {
    margin-right: 8px;
}

// Add tooltips for potentially truncated text
.name, .datetime .value {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}


.btn {
  flex: 1;
  border: none;
  border-radius: 5px;
  font-size: 14px;
  cursor: pointer;
  transition: 0.3s;
  border: 1px solid rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(5px);
}

.btn + .btn {
  margin-left: 8px;
}

.edit-btn {
  background: rgba(0, 123, 255, 0.5);
  color: white;
}

.edit-btn:hover {
  background: rgba(0, 123, 255, 0.8);
}

.delete-btn {
  background: rgba(220, 53, 69, 0.5);
  color: white;
}

.delete-btn:hover {
  background: rgba(220, 53, 69, 0.8);
}


.pagination-container {
  margin-top: 12px;
  display: flex;
  justify-content: center;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.2);
  padding: 8px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Modern Drawer Styling */
.drawer-container {
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.1), rgba(24, 144, 255, 0.05))!important;
  height: 100%;
  overflow: hidden;
  position: relative;

  /* Subtle animated background pattern */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.2) 1px, transparent 1px),
      radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.2) 1px, transparent 1px);
    background-size: 40px 40px;
    opacity: 0.5;
    z-index: 0;
    pointer-events: none;
  }

  /* Override default drawer header styles */
  ::ng-deep .ant-drawer-header {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    padding: 16px 24px;

    .ant-drawer-title {
      color: #1890ff;
      font-size: 20px;
      font-weight: 600;
      position: relative;
      padding-left: 15px;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 18px;
        background: linear-gradient(to bottom, #1890ff, #096dd9);
        border-radius: 2px;
      }
    }

    .ant-drawer-close {
      color: #1890ff;
      transition: all 0.3s ease;

      &:hover {
        color: #40a9ff;
        transform: rotate(90deg);
      }
    }
  }
}

/* Enhanced Form Sections */
.form-section {
  padding: 20px;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16px;
  margin-bottom: 20px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.08);
    background: rgba(255, 255, 255, 0.8);
  }

  /* Section icon indicators */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, #1890ff, #096dd9);
    opacity: 0.7;
  }

  /* Different colors for different sections */
  &:nth-child(2)::before {
    background: linear-gradient(to bottom, #52c41a, #389e0d);
  }

  &:nth-child(3)::before {
    background: linear-gradient(to bottom, #faad14, #d48806);
  }

  h4 {
    margin-bottom: 16px;
    font-weight: 500;
    font-size: 18px;
    color: #333;
    display: flex;
    align-items: center;

    &::before {
      content: '•';
      margin-right: 8px;
      color: #1890ff;
      font-size: 24px;
    }
  }
}

.full-width {
 width: 100%;
}

.half-width {
 width: 48%;
}

.third-width {
 width: 30%;
}

.flex-row {
 display: flex;
 justify-content: space-between;
 gap: 10px;
}

.time-picker {
 margin-top: 10px;
 width: 100%;
}

nz-drawer {
 display: flex;
 flex-direction: column;
 height: 100vh; /* Ensure it takes full viewport height */
}

/* Enhanced Drawer Content */
.drawer-content {
  flex-grow: 1;
  overflow-y: auto;
  padding: 24px;
  padding-bottom: 90px; /* Space for footer */
  position: relative;
  z-index: 1;

  /* Scrollbar styling */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(24, 144, 255, 0.3);
    border-radius: 4px;

    &:hover {
      background: rgba(24, 144, 255, 0.5);
    }
  }

  /* Modern form styling */
  .modern-form {
    display: flex;
    flex-direction: column;
    gap: 24px;

    /* Form field styling */
    ::ng-deep .mat-form-field {
      width: 100%;
      margin-bottom: 16px;

      .mat-form-field-wrapper {
        padding-bottom: 0;
      }

      .mat-form-field-flex {
        background: rgba(255, 255, 255, 0.5);
        border-radius: 8px;
        padding: 0.75em 0.75em 0;
        transition: all 0.3s ease;
      }

      &.mat-focused .mat-form-field-flex {
        background: rgba(255, 255, 255, 0.8);
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
      }

      .mat-form-field-label {
        color: rgba(0, 0, 0, 0.6);
      }

      &.mat-focused .mat-form-field-label {
        color: #1890ff;
      }

      .mat-form-field-underline {
        background-color: rgba(0, 0, 0, 0.1);
      }

      .mat-form-field-ripple {
        background-color: #1890ff;
      }
    }

    /* Checkbox styling */
    ::ng-deep .mat-checkbox {
      .mat-checkbox-frame {
        border-color: rgba(0, 0, 0, 0.3);
        border-radius: 4px;
      }

      .mat-checkbox-background {
        background-color: #1890ff;
      }

      .mat-checkbox-label {
        color: rgba(0, 0, 0, 0.7);
        font-weight: 500;
      }
    }

    /* Date picker styling */
    ::ng-deep nz-date-picker, ::ng-deep nz-time-picker {
      width: 100%;
      margin-bottom: 16px;

      .ant-picker {
        background: rgba(255, 255, 255, 0.5);
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        padding: 8px 12px;
        transition: all 0.3s ease;

        &:hover, &.ant-picker-focused {
          background: rgba(255, 255, 255, 0.8);
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
    }

    /* Upload button styling */
    ::ng-deep nz-upload button {
      background: linear-gradient(135deg, #1890ff, #096dd9);
      border: none;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
        background: linear-gradient(135deg, #40a9ff, #1890ff);
      }

      &:active {
        transform: translateY(0);
      }
    }

    /* Current file display */
    .current-file {
      background: rgba(24, 144, 255, 0.1);
      border-radius: 8px;
      padding: 12px;
      border-left: 4px solid #1890ff;

      p {
        margin: 0;
        color: #333;
        font-size: 14px;

        strong {
          color: #1890ff;
          margin-right: 8px;
        }
      }
    }

    /* Error message styling */
    mat-error {
      font-size: 12px;
      margin-top: 4px;
      display: flex;
      align-items: center;

      &::before {
        content: '⚠️';
        margin-right: 6px;
        font-size: 14px;
      }
    }
  }
}

/* Enhanced Footer with Modern Buttons */
.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.08);
  border-top: 1px solid rgba(255, 255, 255, 0.5);
  z-index: 10;

  button {
    min-width: 120px;
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    border: none;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 70%);
      opacity: 0;
      transform: scale(0.5);
      transition: all 0.3s ease;
    }

    &:hover {
      transform: translateY(-3px);

      &::before {
        opacity: 0.8;
        transform: scale(2);
      }

      i {
        transform: scale(1.2);
      }
    }

    &:active {
      transform: translateY(0);
    }

    i {
      font-size: 18px;
      transition: transform 0.3s ease;
      position: relative;
      z-index: 1;
    }

    &.btn-dark {
      background: rgba(0, 0, 0, 0.7);
      color: white;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

      &:hover {
        background: rgba(0, 0, 0, 0.8);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
      }
    }

    &.btn-success {
      background: linear-gradient(135deg, #52c41a, #389e0d);
      color: white;
      box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);

      &:hover {
        background: linear-gradient(135deg, #73d13d, #52c41a);
        box-shadow: 0 6px 16px rgba(82, 196, 26, 0.4);
      }

      &:disabled {
        background: #d9d9d9;
        color: rgba(0, 0, 0, 0.25);
        box-shadow: none;
        cursor: not-allowed;

        &:hover {
          transform: none;
        }
      }
    }
  }

  @media (max-width: 480px) {
    padding: 12px 16px;

    button {
      min-width: 0;
      padding: 8px 12px;
      font-size: 14px;

      span:not(.anticon) {
        display: none;
      }
    }
  }
}

/* Additional styles for enhanced drawer form elements */
.schedule-toggle {
  margin-bottom: 20px;

  .checkbox-label {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }
}

.schedule-fields {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 16px;
  margin-top: 16px;
  border: 1px dashed rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.4);
    border-color: rgba(24, 144, 255, 0.5);
  }

  h5 {
    font-size: 15px;
    font-weight: 500;
    color: #1890ff;
    margin-bottom: 12px;
    position: relative;
    padding-left: 12px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 14px;
      background: #1890ff;
      border-radius: 2px;
    }
  }
}

.date-time-group {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.date-time-row {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;

  .date-field, .time-field {
    flex: 1;

    label {
      display: block;
      margin-bottom: 6px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
      font-weight: 500;
    }
  }
}

.required-mark {
  color: #f5222d;
  margin-left: 2px;
}

.optional-mark {
  color: #8c8c8c;
  font-size: 12px;
  font-style: italic;
}

.file-upload-container {
  margin-top: 16px;

  label {
    display: block;
    margin-bottom: 12px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    font-weight: 500;
  }
}

/* Info Dialog Styles */
::ng-deep .info-modal {
  .ant-modal-content {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
  }

  .ant-modal-header {
    background: linear-gradient(135deg, #1890ff, #096dd9);
    padding: 16px 24px;
    border-bottom: none;

    .ant-modal-title {
      color: white;
      font-size: 18px;
      font-weight: 600;
    }
  }

  .ant-modal-body {
    padding: 24px;
  }

  .ant-modal-footer {
    border-top: none;
    padding: 16px 24px;
  }

  .info-dialog {
    h3 {
      font-size: 20px;
      font-weight: 600;
      color: #1890ff;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 1px solid rgba(24, 144, 255, 0.2);
    }

    .info-item {
      display: flex;
      margin-bottom: 12px;
      padding: 8px 12px;
      background: rgba(240, 240, 240, 0.3);
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(240, 240, 240, 0.6);
      }

      strong {
        min-width: 100px;
        color: #333;
        font-weight: 600;
      }
    }
  }

  .ant-btn-primary {
    background: linear-gradient(135deg, #1890ff, #096dd9);
    border: none;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);

    &:hover {
      background: linear-gradient(135deg, #40a9ff, #1890ff);
      box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
    }
  }
}
