//
import { Component, OnInit } from '@angular/core';
import { environment } from 'src/environments/environment'; // Import environment
import { SharedModule } from 'src/app/theme/shared/shared.module';
import { PageService } from '../page.service';
import { StorageService } from 'src/app/theme/shared/service/storage.service';
import { CommonModule } from '@angular/common'; // Import CommonModule
import { FormsModule } from '@angular/forms';
import { NzUploadChangeParam } from 'ng-zorro-antd/upload';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import ValidatePermissionComponent from "../../../theme/shared/components/validate-permission/validate-permission.component";
declare var cuteAlert: any;

@Component({
  selector: 'app-screen-control',
  standalone: true, // Use standalone component flag
  imports: [
    CommonModule, // Needed for *ngIf, *ngFor, | date pipe etc.
    SharedModule, // Keep if it provides other shared directives/pipes/modules you need
    FormsModule, // For ngModel
    NzInputModule, // For nz-input-group
    NzButtonModule, // For nz-button
    NzIconModule,
    ValidatePermissionComponent
],
  templateUrl: './screen-control.component.html',
  styleUrl: './screen-control.component.scss'
})
export default class ScreenControlComponent implements OnInit { // Added OnInit interface
  listOfData: any[] = [];
  listOfDevices: any[] = [];
  loading = false;
  deviceModel:any;
  priorityModel:any;

  // Search and sort properties
  searchText: string = '';
  sortField: string = 'status';
  sortOrder: 'ascend' | 'descend' = 'ascend';
  filteredData: any[] = []; // Data after search/filter but before pagination

  IsScheduledModel:any;
  selectedUser: any;
  selectedFile: any;
  file: File | null = null;
  fileDetails: any;
  startDateModel:any;
  startTimeModel:any;
  endDateModel:any;
  endTimeModel:any;
  combinedStartDateTime: Date | null = null;
  combinedEndDateTime: Date | null = null;
  errors: any = {};
  fileName: any;

  // Properties for edit functionality
  selectedPosting: any = null;
  editMode = false;

  // Properties for video playback
  currentlyPlayingId: number | null = null;
  isVideoPlaying = false;
  priorityLabel = [
    { id: 0, name: 'Low' },
    { id: 1, name: 'Medium' },
    { id: 2, name: 'High' },
    { id: 3, name: 'Critical' }
  ];
  statusLabel = [
    { id: 0, name: 'Draft', color: '#df9031' }, // Example status for 0
    { id: 1, name: 'Online', color: 'green' },  // Example status for 1
    { id: 2, name: 'Scheduled', color: 'blue' }, // Example
    { id: 3, name: 'Error', color: 'red' }     // Example
    // Add other statuses as defined by your API
  ];
  months: any[] = [
    {id:1,name:'መስከረም'},
    {id:2,name:'ጥቅምት'},
    {id:3,name:'ኅዳር'},
    {id:4,name:'ታኅሣሥ'},
    {id:5,name:'ጥር'},
    {id:6,name:'የካቲት'},
    {id:7,name:'መጋቢት'},
    {id:8,name:'ሚያዝያ'},
    {id:9,name:'ግንቦት'},
    {id:10,name:'ሰኔ'},
    {id:11,name:'ሐምሌ'},
    {id:12,name:'ነሐሴ'},
    {id:13,name:'ጳጐሜ'},
  ];

  years:any[] = [];
  days:any[] = [];

  showUpdate = false;
  showAdd = false;
  // --- Added for Mini Preview ---
  baseApiUrl = environment.IP; // Get base URL from environment
  // --- End Mini Preview Additions ---
  dayStartDateModel:any;
  monthStartDateModel:any;
  yearStartDateModel:any;

  dayEndDateModel:any;
  monthEndDateModel:any;
  yearEndDateModel:any;
  ethiopianDate:any;
  isPageAllowed:any;


  dayStartDateEditModel:any;
  monthStartDateEditModel:any;
  yearStartDateEditModel:any;

  dayEndDateEditModel:any;
  monthEndDateEditModel:any;
  yearEndDateEditModel:any;


  token = this._storageService.getToken(); // It's often better to get the token inside methods if it can expire/change

  constructor(
    private _pageService: PageService,
    private _storageService: StorageService,
    private nzModalService: NzModalService
  ) {}

  generateDays(){
    for(let i=1; i<=30; i++){
      this.days.push(i);
    }
  }

  generateYears(){
    for(let i=2016; i<=3080; i++){
      this.years.push(i);
    }
  }

  // correctAndUpdateStartTime(selectedLocalTime: Date | null, data: ItemData): void {
  //   let targetUtcDate: Date | null = null;

  //   if (selectedLocalTime instanceof Date) {
  //     const localHours = selectedLocalTime.getHours();
  //     const localMinutes = selectedLocalTime.getMinutes();
  //     const localSeconds = selectedLocalTime.getSeconds();
  //     targetUtcDate = new Date(0); // Base date (1970-01-01 UTC)
  //     targetUtcDate.setUTCHours(localHours, localMinutes, localSeconds, 0); // Set UTC H:M:S
  //   } // else targetUtcDate remains null

  //   // 1. Update the main data model
  //   data.start_time = targetUtcDate;

  //   // 2. Update the temporary display model in the map
  //   if (this.editTimeModels[data.id]) {
  //      this.editTimeModels[data.id].start = selectedLocalTime; // Use the picker's value
  //   }

  //   // 3. Update the duration display
  //   this.updateDurationDisplay(data);
  //   // this.ref.markForCheck(); // If using OnPush
  // }


  ngOnInit() { // Implement OnInit
    // Ensure baseApiUrl doesn't end with a slash if filePaths start with one
    if (this.baseApiUrl && this.baseApiUrl.endsWith('/')) {
        this.baseApiUrl = this.baseApiUrl.slice(0, -1);
    }
    this.getAllScreenPosting();
    this.getAllDevices();
    // Initialize display data
    this.updateDisplayData();
    this.generateDays();
    this.generateYears();
    this.getCurrentDate();
  }

  handleChange(info: NzUploadChangeParam): void {
    if (info.file.originFileObj) {
      // Create an NzUploadFile object with uid and other necessary properties
      this.file = info.file.originFileObj;
      this.selectedFile = this.file.name;
    }
  }

  // combineDateAndTime(datePart: Date | null, timePart: Date | null): Date | null {
  //   if (!datePart || !timePart) {
  //     return null; // Can't combine if either part is missing
  //   }

  //   // Create a new Date object based on the datePart to avoid modifying the original
  //   const combined = new Date(datePart);

  //   // Set the time components from the timePart
  //   combined.setHours(timePart.getHours());
  //   combined.setMinutes(timePart.getMinutes());
  //   combined.setSeconds(timePart.getSeconds());
  //   combined.setMilliseconds(timePart.getMilliseconds()); // Optional, but good practice

  //   return combined;
  // }

  onDateTimeChange(): void {
    // this.combinedStartDateTime = this.combineDateAndTime(this.startDateModel, this.startTimeModel);
    // this.combinedEndDateTime = this.combineDateAndTime(this.endDateModel, this.endTimeModel);
    // Add any validation logic here (e.g., ensure start is before end)
    this.validateDates();
  }

  validateDates(): void {
    // if (this.combinedStartDateTime && this.combinedEndDateTime) {
    //   if (this.combinedStartDateTime >= this.combinedEndDateTime) {
    //     console.error('Validation Error: Start date/time must be before end date/time.');
    //     // You might want to set a form control error or display a message
    //   } else {
    //      console.log('Dates are valid.');
    //   }
    // }
  }

  async getCurrentDate() {
   const currentDate = new Date();
   const year = currentDate.getFullYear();      // 2025
   const month = currentDate.getMonth() + 1;    // 5 (months are 0-indexed)
   const day = currentDate.getDate();
   this._pageService.toEthiopian(year, month, day,this.token).subscribe({
     next: (response) => {
        this.ethiopianDate = response;
        this.dayStartDateModel = this.ethiopianDate[2]
        this.dayEndDateModel = this.ethiopianDate[2]
        this.monthStartDateModel = this.ethiopianDate[1]
        this.monthEndDateModel = this.ethiopianDate[1]
        this.yearStartDateModel = this.ethiopianDate[0]
        this.yearEndDateModel = this.ethiopianDate[0]
      },
      error: (error) => {
        console.error(error);
      }
    });

  }



  // onSubmit() {
  //   let data = {
  //       deviceId: this.deviceModel,
  //       priority: this.priorityModel,
  //       isScheduled: this.IsScheduledModel,
  //       selectedFile: this.selectedFile,
  //     }
  //     if(this.IsScheduledModel){
  //     const startDate = this.yearStartDateModel + "-" + this.monthStartDateModel + "-" + this.dayStartDateModel;
  //     const endDate = this.yearEndDateModel + "-" + this.monthEndDateModel + "-" + this.dayEndDateModel;
  //     const start = startDate;
  //     const end = endDate;
  //       data['startDate'] = start;
  //       data['endDate'] = end;
  //     }

  //   if (!this.validateForm(data)) {
  //     return; // Exit early if form is invalid
  //   }
  //   this.loading = true;
  //   const formData = new FormData();

  //   formData.append('file', this.file as any);
  //   formData.append('deviceId', this.deviceModel as any);
  //   formData.append('priority', this.priorityModel as any);
  //   if(this.IsScheduledModel){
  //     const startDate = this.yearStartDateModel + "-" + this.monthStartDateModel + "-" + this.dayStartDateModel;
  //     const endDate = this.yearEndDateModel + "-" + this.monthEndDateModel + "-" + this.dayEndDateModel;
  //     const start = startDate;
  //     const end = endDate;
  //     const startTime = this.startTimeModel;
  //     const endTime =  this.endTimeModel;
  //     formData.append('startDate', start as any);
  //     formData.append('endDate', end as any);
  //     formData.append('startTime', startTime as any);
  //     formData.append('endTime', endTime as any);
  //   }
  //   formData.append('isScheduled', this.IsScheduledModel?1:0 as any);

  //   this._pageService.CreatePosting(formData,this.token).subscribe({
  //     next: (_res: any) => {
  //       this.getAllScreenPosting();
  //       this.loading = false;
  //       this.closeCreate();
  //       this.clear();
  //       this.Alert('success', 'Success', 'Screen posting created successfully!');
  //     },
  //     error: (err: any) => {
  //       console.error('Error creating screen posting:', err);
  //       this.loading = false;
  //       this.Alert('error', 'Error', 'Failed to create screen posting!');
  //     }
  //   });
  // }

    onSubmit() {
    let data = {
        deviceId: this.deviceModel,
        priority: this.priorityModel,
        isScheduled: this.IsScheduledModel,
        selectedFile: this.selectedFile,
        // Remove startDate, endDate string construction here
      }

    // Validation will happen in validateForm, ensure it checks all fields
    if (!this.validateForm(data)) {
      return; // Exit early if form is invalid
    }

    this.loading = true;
    const formData = new FormData();

    formData.append('file', this.file as any);
    formData.append('deviceId', this.deviceModel as any);
    formData.append('priority', this.priorityModel as any);
    // Ensure isScheduled is sent as a number 0 or 1
    formData.append('isScheduled', this.IsScheduledModel ? '1' : '0'); // FormData works better with strings

    if (this.IsScheduledModel) {
      // Append ETHIOPIAN date components as numbers or strings
      formData.append('yearStartDateModel', this.yearStartDateModel as any);
      formData.append('monthStartDateModel', this.monthStartDateModel as any); // Assuming 1-based month
      formData.append('dayStartDateModel', this.dayStartDateModel as any);

      formData.append('yearEndDateModel', this.yearEndDateModel as any);
      formData.append('monthEndDateModel', this.monthEndDateModel as any);   // Assuming 1-based month
      formData.append('dayEndDateModel', this.dayEndDateModel as any);




    }

    // ... (Rest of your API call logic, same as before)
    this._pageService.CreatePosting(formData,this.token).subscribe({
      next: (_res: any) => {
        this.getAllScreenPosting();
        this.loading = false;
        this.closeCreate();
        this.clear();
        this.Alert('success', 'Success', 'Screen posting created successfully!');
      },
      error: (err: any) => {
        console.error('Error creating screen posting:', err);
         // Log the error details received from the backend
        console.error('Backend error response:', err.error); // Assuming err object has an 'error' property with backend details

        this.loading = false;
        // Attempt to show a more specific error message if the backend provided one
        const errorMessage = err.error && err.error.message ? err.error.message : 'Failed to create screen posting!';
        this.Alert('error', 'Error', errorMessage);
      }
    });
  }

  clear(){
    this.deviceModel = null;
    this.priorityModel = null;
    this.endDateModel = null;
    this.startDateModel = null;
    this.startTimeModel = null;
    this.endTimeModel = null;
    this.IsScheduledModel = null;
    this.selectedFile = null;
    this.selectedUser = null;
    this.file = null;
    this.fileName = null;
    this.editMode = false;
    this.selectedPosting = null;
    this.errors = {};
  }

  Alert(type: string, title: string, msg: string): void {
    cuteAlert({
      type: type,
      title: title,
      message: msg,
      buttonText: 'እሺ'
    });
  }

  openImage(url: string) {
    // Create a new window or tab to open the image
    window.open(url, '_blank');
  }

  validateForm(data: any): boolean {
    this.errors = {}; // Reset errors

    if (!data.deviceId) {
      this.errors.deviceId = 'Screen must be selected!';
    }

    if (!data.priority) {
      this.errors.priority = 'Priority must be selected!';
    }

    // In edit mode, a file is required only if there's no existing file
    // In create mode, a file is always required
    if (!this.editMode && !data.selectedFile) {
      this.errors.selectedFile = 'select a file!';
    } else if (this.editMode && !data.selectedFile && !this.fileName) {
      this.errors.selectedFile = 'select a file!';
    }

    if (!data.startDate && data.isScheduled === 1) {
      this.errors.startDate  = 'Please select a start date!';
    }
    if (!data.endDate && data.isScheduled === 1) {
      this.errors.numberOfDays  = 'Please select a end date!';
    }

    return Object.keys(this.errors).length === 0; // Return true if no errors
  }


  onChange(_result: Date[]): void {
    this.onDateTimeChange();
  }

  async getAllScreenPosting() {
    this.loading = true;
    this.listOfData = [];
    this.displayData = [];
    // Use arrow functions for subscribe callbacks to maintain 'this' context
    this._pageService.GetPosting(this.token).subscribe({
      next: (res: any) => { // Use arrow function
        this.listOfData = res.data || []; // Default to empty array if data is null/undefined
        this.totalItems = this.listOfData.length;
        this.updateDisplayData(); // Update the display data based on pagination
        this.loading = false;
      },
      error: (err: any) => { // Use arrow function
        console.error('Error fetching screen postings:', err); // Log error
        // Potentially show a user-friendly error message here (e.g., using NzMessageService)
        this.listOfData = []; // Clear data on error
        this.displayData = [];
        this.totalItems = 0;
        this.loading = false;
      }
    });
  }

  async getAllDevices() {
    this.loading = true;
    this.listOfDevices = [];
    this._pageService.GetDevices(this.token).subscribe({
      next: (res: any) => {
        this.listOfDevices = res.data || [];
        this.loading = false;
      },
      error: (err: any) => {
        console.error('Error fetching devices:', err);
        this.listOfDevices = [];
        this.loading = false;
      }
    });
  }

  closeUpdate(){
    this.showUpdate = false;
    this.selectedPosting = null;
    this.editMode = false;
    this.clear();
  }

  openUpdate(data: any, event?: MouseEvent) {
    if (event) {
      event.stopPropagation();
    }
    this.selectedPosting = data;
    this.editMode = true;
    this.populateEditForm(data);
    this.showUpdate = true;
  }

  populateEditForm(data: any) {

    // Populate form fields with the selected posting data
    this.deviceModel = Number(data.deviceId);

    this.priorityModel = data.priority;
    this.IsScheduledModel = data.isScheduled === 1;
    this.fileName = this.getFileName(data.filePath);

    if (data.isScheduled === 1 && data.startDate && data.endDate) {
      // Convert string dates to Date objects
      const startDate = new Date(data.startDate);
      const endDate = new Date(data.endDate);

      // Set date models
      this.startDateModel = startDate;
      this.endDateModel = endDate;


    }
  }

  onUpdateSubmit() {
    if (!this.selectedPosting) {
      this.Alert('error', 'Error', 'No posting selected for update!');
      return;
    }

    let data = {
      deviceId: this.deviceModel,
      priority: this.priorityModel,
      isScheduled: this.IsScheduledModel ? 1 : 0,
    };

    if (this.IsScheduledModel) {
      const startDate = this.yearStartDateModel + "-" + this.monthStartDateModel + "-" + this.dayStartDateModel;
      const endDate = this.yearEndDateModel + "-" + this.monthEndDateModel + "-" + this.dayEndDateModel;
      const start = startDate;
      const end = endDate;
      data['startDate'] = start;
      data['endDate'] = end;
    }

    if (!this.validateForm(data)) {
      return; // Exit early if form is invalid
    }

    this.loading = true;
    const formData = new FormData();

    // Only append file if a new one is selected
    if (this.file) {
      formData.append('file', this.file as any);
    }

    formData.append('deviceId', this.deviceModel as any);
    formData.append('priority', this.priorityModel as any);

    if (this.IsScheduledModel) {


      formData.append('yearStartDateModel', this.yearStartDateEditModel as any);
      formData.append('monthStartDateModel', this.monthStartDateModel as any);
      formData.append('dayStartDateModel', this.dayStartDateEditModel as any);

       formData.append('yearEndDateModel', this.yearEndDateEditModel as any);
      formData.append('monthEndDateModel', this.monthEndDateEditModel as any);
      formData.append('dayEndDateModel', this.dayEndDateEditModel as any);

    }

    formData.append('isScheduled', this.IsScheduledModel ? 1 : 0 as any);

    this._pageService.UpdatePosting(this.selectedPosting.id, formData, this.token).subscribe({
      next: (_res: any) => {
        this.getAllScreenPosting();
        this.loading = false;
        this.closeUpdate();
        this.Alert('success', 'Success', 'Screen posting updated successfully!');
      },
      error: (err: any) => {
        console.error('Error updating screen posting:', err);
        this.loading = false;
        this.Alert('error', 'Error', 'Failed to update screen posting!');
      }
    });
  }

  closeCreate(){
    this.showAdd = false;
  }

  openCreate() {
    this.showAdd = true;
  }

  filterStatus(statusId: number, isScheduled: number): { id: number; name: string; color: string } | null {
    let status: { id: number; name: string; color: string } | undefined;
    if(isScheduled === 1) {
       const id = statusId === 0 ? 2 : statusId;
      status = this.statusLabel.find(x => x.id === id);
      return status ?? null; // Return null if not found for safer template access
    }else{
      status = this.statusLabel.find(x => x.id === statusId);
      return status ?? null; // Return null if not found for safer template access
    }

  }




  // --- Added for Mini Preview ---
  getFileName(filePath: string): string {
    if (!filePath) {
      return 'N/A';
    }
    // Handles both / and \ separators
    const lastSlash = Math.max(filePath.lastIndexOf('/'), filePath.lastIndexOf('\\'));
    return filePath.substring(lastSlash + 1);
  }

  onImageError(event: Event) {
    const imgElement = event.target as HTMLImageElement;
    // Option: Add a class to the parent to show a placeholder via CSS
    imgElement.parentElement?.classList.add('has-error');
    // Option: Directly hide the image (less flexible than CSS approach)
    // imgElement.style.display = 'none';
  }

  onVideoError(event: Event) {
    const videoElement = event.target as HTMLVideoElement;
    // Add a class to the parent to show a placeholder via CSS
    videoElement.parentElement?.classList.add('has-error');
    console.error('Video failed to load:', videoElement.src);

    // Reset playing state if this was the current video
    if (this.currentlyPlayingId && videoElement.parentElement?.parentElement?.classList.contains('playing')) {
      this.isVideoPlaying = false;
      this.currentlyPlayingId = null;
    }
  }
  // --- End Mini Preview Additions ---

  // --- Action Button Placeholders (Implement actual logic) ---


  deleteItem(id: number, event?: Event) {
    if (event) {
      event.stopPropagation();
    }

    this.loading = true;

    this._pageService.DeletePosting(id, this.token).subscribe({
      next: (_res: any) => {
        this.getAllScreenPosting();
        this.loading = false;
        this.Alert('success', 'Success', 'Screen posting deleted successfully!');
      },
      error: (err: any) => {
        console.error('Error deleting screen posting:', err);
        this.loading = false;
        this.Alert('error', 'Error', 'Failed to delete screen posting!');
      }
    });
  }

  openInfo(data: any, event: MouseEvent) {
    event.stopPropagation();

    // Create a formatted message with posting details
    const deviceName = this.listOfDevices.find(d => d.id === data.deviceId)?.name || 'Unknown';
    const statusObj = this.filterStatus(data.status, data.isScheduled);
    const statusName = statusObj ? statusObj.name : 'Unknown';

    let infoMessage = `
      <div class="info-dialog">
        <h3>Posting Details</h3>
        <div class="info-item"><strong>ID:</strong> ${data.id}</div>
        <div class="info-item"><strong>Screen:</strong> ${deviceName}</div>
        <div class="info-item"><strong>Status:</strong> ${statusName}</div>
        <div class="info-item"><strong>Priority:</strong> ${data.priority}</div>
        <div class="info-item"><strong>File Type:</strong> ${data.fileType || 'Unknown'}</div>
        <div class="info-item"><strong>Created:</strong> ${new Date(data.createdAt).toLocaleString()}</div>
    `;

    if (data.isScheduled) {
      infoMessage += `
        <div class="info-item"><strong>Start Date:</strong> ${new Date(data.startDate).toLocaleString()}</div>
        <div class="info-item"><strong>End Date:</strong> ${new Date(data.endDate).toLocaleString()}</div>
      `;
    }

    infoMessage += '</div>';

    // Show the info in a modal
    this.nzModalService.create({
      nzTitle: 'Posting Information',
      nzContent: infoMessage,
      nzWidth: 500,
      nzFooter: [
        {
          label: 'Close',
          type: 'primary',
          onClick: (componentInstance: any) => {
            componentInstance.close();
          }
        }
      ],
      nzMaskClosable: true,
      nzCentered: true,
      nzBodyStyle: { padding: '16px' },
      nzClassName: 'info-modal'
    });
  }

  previewFile(data: any, event: MouseEvent) {
     event.stopPropagation();
     console.log('Preview Full:', data);
     if (data.filePath) {
        // Construct the full URL carefully
        const fileUrl = this.baseApiUrl + data.filePath;
        window.open(fileUrl, '_blank'); // Open in new tab
     }
   }

   /**
    * Play or pause a video in the card
    * @param data The posting data containing the video
    * @param event The mouse event
    * @param videoElement Reference to the video element
    */
   toggleVideoPlayback(data: any, event: MouseEvent, videoElement: HTMLVideoElement) {
     event.stopPropagation();

     // If we're clicking on the same video that's already playing
     if (this.currentlyPlayingId === data.id && this.isVideoPlaying) {
       videoElement.pause();
       this.isVideoPlaying = false;
       this.currentlyPlayingId = null;
     }
     // If we're clicking on a different video or a video that's not playing
     else {
       // If there's another video playing, we need to stop it first
       if (this.currentlyPlayingId !== null && this.currentlyPlayingId !== data.id) {
         // Find all video elements and pause any that might be playing
         const videos = document.querySelectorAll('video');
         videos.forEach(video => video.pause());
       }

       // Play the clicked video
       videoElement.play();
       this.isVideoPlaying = true;
       this.currentlyPlayingId = data.id;

       // Add event listener to handle when video ends
       videoElement.onended = () => {
         this.isVideoPlaying = false;
         this.currentlyPlayingId = null;
       };
     }
   }


   // --- End Action Button Placeholders ---

   // --- Pagination Implementation ---
   currentPage: number = 1;
   pageSize: number = 7; // Show 9 cards per page (3x3 grid)
   totalItems: number = 0;
   displayData: any[] = []; // Data to display after pagination

   onPageChange(page: number): void {
     this.currentPage = page;
     this.updateDisplayData();
   }

   onPageSizeChange(size: number): void {
     this.pageSize = size;
     this.currentPage = 1; // Reset to first page
     this.updateDisplayData();
   }

   // Search method
  onSearch(): void {
    this.currentPage = 1; // Reset to first page when searching
    this.updateDisplayData();
  }

  // Sort method
  onSort(field: string): void {
    // If clicking the same field, toggle sort order
    if (this.sortField === field) {
      this.sortOrder = this.sortOrder === 'ascend' ? 'descend' : 'ascend';
    } else {
      this.sortField = field;
      this.sortOrder = 'ascend'; // Default to ascending when changing fields
    }
    this.updateDisplayData();
  }

  // Get effective status for sorting (implements the required order: online(1), scheduled(2), draft(0))
  getEffectiveStatus(item: any): number {
    if (item.status === 1) return 1; // Online
    if (item.isScheduled === 1) return 2; // Scheduled
    return 3; // Draft (0) - we use 3 to make it last in the sort order
  }

  updateDisplayData(): void {
    // First, filter the data based on search text
    this.filteredData = this.listOfData.filter(item => {
      if (!this.searchText) return true;

      const searchLower = this.searchText.toLowerCase();
      // Search in filename
      const fileName = this.getFileName(item.filePath)?.toLowerCase() || '';
      // Search in device name
      const deviceName = this.listOfDevices.find(d => d.id === item.deviceId)?.name?.toLowerCase() || '';
      // Search in status
      const status = this.filterStatus(item.status, item.isScheduled)?.name?.toLowerCase() || '';

      return fileName.includes(searchLower) ||
             deviceName.includes(searchLower) ||
             status.includes(searchLower);
    });

    // Then, sort the filtered data
    this.filteredData = [...this.filteredData].sort((a, b) => {
      let comparison = 0;

      if (this.sortField === 'status') {
        // Custom sort order for status: online(1), scheduled(2), draft(0)
        const statusA = this.getEffectiveStatus(a);
        const statusB = this.getEffectiveStatus(b);
        comparison = statusA - statusB;
      } else if (this.sortField === 'date') {
        // Sort by creation date
        comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      } else if (this.sortField === 'name') {
        // Sort by file name
        const nameA = this.getFileName(a.filePath).toLowerCase();
        const nameB = this.getFileName(b.filePath).toLowerCase();
        comparison = nameA.localeCompare(nameB);
      } else if (this.sortField === 'device') {
        // Sort by device name
        const deviceA = this.listOfDevices.find(d => d.id === a.deviceId)?.name?.toLowerCase() || '';
        const deviceB = this.listOfDevices.find(d => d.id === b.deviceId)?.name?.toLowerCase() || '';
        comparison = deviceA.localeCompare(deviceB);
      }

      // Reverse the comparison if sort order is descending
      return this.sortOrder === 'descend' ? -comparison : comparison;
    });

    // Finally, apply pagination
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.totalItems = this.filteredData.length;
    this.displayData = this.filteredData.slice(startIndex, endIndex);
  }
   // --- End Pagination Implementation ---


   //Post to the screen
   async postToScreen(id: number, event?: Event) {
    if (event) {
      event.stopPropagation();
    }
    this.loading = true;
    this._pageService.postToScreen(id, this.token).subscribe({
      next: (_res: any) => {
        this.getAllScreenPosting();
        this.loading = false;
        this.Alert('success', 'Success', 'Posted successfully!');
      },
      error: (err: any) => {
        console.error('Error posting screen posting:', err);
        this.loading = false;
        this.Alert('error', 'Error', 'Failed to post screen posting!');
      }
    });
   }

   async unpostToScreen(id: number, event?: Event) {
    if (event) {
      event.stopPropagation();
    }
    this.loading = true;
    this._pageService.unpostToScreen(id, this.token).subscribe({
      next: (_res: any) => {
        this.getAllScreenPosting();
        this.loading = false;
        this.Alert('success', 'Success', 'Unposted successfully!');
      },
      error: (err: any) => {
        console.error('Error unposting screen posting:', err);
        this.loading = false;
        this.Alert('error', 'Error', 'Failed to unpost screen posting!');
      }
    });
   }
}
