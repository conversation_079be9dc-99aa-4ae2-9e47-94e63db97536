<app-validate-permission  [pageId]="'accounts'" (permissionChecked)="isPageAllowed = $event"></app-validate-permission>
<div *ngIf="isPageAllowed" class="accounts-container">
  <div class="action-header">
    <h2 class="page-title">User Accounts</h2>
    <button nz-button nzType="primary" (click)="openCreateModal()">
      <i class="feather icon-user-plus"></i> Add User
    </button>
  </div>

  <div class="table-actions">
    <nz-input-group [nzSuffix]="suffixIconSearch" class="search-input">
      <input type="text" nz-input placeholder="Search users..." [(ngModel)]="searchText" (ngModelChange)="filterUsers()" />
    </nz-input-group>
    <ng-template #suffixIconSearch>
      <i class="feather icon-search"></i>
    </ng-template>
    <nz-select [(ngModel)]="statusFilter" (ngModelChange)="filterUsers()" class="status-filter">
      <nz-option nzValue="all" nzLabel="All Status"></nz-option>
      <nz-option nzValue="1" nzLabel="Active"></nz-option>
      <nz-option nzValue="0" nzLabel="Inactive"></nz-option>
    </nz-select>
  </div>
  <i class="feather icon-users"></i>
  <nz-table
    #userTable
    [nzData]="filteredUsers"
    [nzLoading]="isTableLoading"
    nzBordered
    nzSize="middle"
    class="modern-table mt-3"
    [nzPageSize]="10"
    [nzShowSizeChanger]="true"
  >
    <thead>
      <tr>
        <th>Full Name</th>
        <th>Username</th>
        <th>Status</th>
        <th>Created</th>
        <th>Updated</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let user of userTable.data" class="user-row">
        <td><span class="user-name">{{ user.fullName }}</span></td>
        <td><span class="username">{{ user.username }}</span></td>
        <td>
          <span class="status-badge" [class.active]="user.status === 1" [class.inactive]="user.status === 0">
            <i class="feather" [ngClass]="user.status === 1 ? 'icon-check-circle' : 'icon-clock'"></i>
            {{ user.status === 1 ? 'Active' : 'Inactive' }}
          </span>
        </td>
        <td>{{ user.createdAt | date:'medium' }}</td>
        <td>{{ user.updatedAt | date:'medium' }}</td>
        <td class="action-buttons">
          <button nz-button nzType="primary" nzShape="circle" nzSize="small" (click)="openEditModal(user)" nz-tooltip nzTooltipTitle="Edit User">
            <i class="feather icon-edit-2"></i>
          </button>
          <button nz-button nzType="default" nzShape="circle" nzSize="small" (click)="openPasswordModal(user)" nz-tooltip nzTooltipTitle="Change Password">
            <i class="feather icon-lock"></i>
          </button>
          <button *ngIf="user.status === 0" nz-button nzType="default" nzShape="circle" nzSize="small" (click)="activateUser(user)" nz-tooltip nzTooltipTitle="Activate User">
            <i class="feather icon-check-circle active-icon"></i>
          </button>
          <button *ngIf="user.status === 1" nz-button nzType="default" nzShape="circle" nzSize="small" (click)="deactivateUser(user)" nz-tooltip nzTooltipTitle="Deactivate User">
            <i class="feather icon-x-circle inactive-icon"></i>
          </button>
        </td>
      </tr>
    </tbody>
  </nz-table>

  <!-- Create User Modal -->
  <nz-modal
    [(nzVisible)]="isCreateModalVisible"
    nzTitle="Create User"
    (nzOnCancel)="handleCreateCancel()"
    (nzOnOk)="handleCreateOk()"
    [nzOkLoading]="isModalLoading"
     nzOkText="Create"
  >
  <ng-container *nzModalContent>
    <form [formGroup]="createForm" class="modern-form">
      <div class="form-item">
        <label>Full Name</label>
        <input nz-input formControlName="fullName" placeholder="Full Name" />
        <div *ngIf="createForm.get('fullName')?.invalid && (createForm.get('fullName')?.dirty || createForm.get('fullName')?.touched || submitForm)" class="error-message">
          <span *ngIf="createForm.get('fullName')?.errors?.['required']">Full name is required</span>
          <span *ngIf="createForm.get('fullName')?.errors?.['minlength']">Full name must be at least 3 characters</span>
        </div>
      </div>

      <div class="form-item">
        <label>Username</label>
        <input nz-input formControlName="username" placeholder="Username" />
        <div *ngIf="createForm.get('username')?.invalid && (createForm.get('username')?.dirty || createForm.get('username')?.touched || submitForm)" class="error-message">
          <span *ngIf="createForm.get('username')?.errors?.['required']">Username is required</span>
          <span *ngIf="createForm.get('username')?.errors?.['pattern']">Username must contain only letters, numbers, and underscores</span>
        </div>
      </div>

      <div class="form-item">
        <label>Password</label>
        <input nz-input type="password" formControlName="password" placeholder="Password" />
        <div *ngIf="createForm.get('password')?.invalid && (createForm.get('password')?.dirty || createForm.get('password')?.touched || submitForm)" class="error-message">
          <span *ngIf="createForm.get('password')?.errors?.['required']">Password is required</span>
          <span *ngIf="createForm.get('password')?.errors?.['minlength']">Password must be at least 6 characters</span>
        </div>
      </div>

    </form>
  </ng-container>
  </nz-modal>

  <!-- Edit User Modal -->
  <nz-modal
    [(nzVisible)]="isEditModalVisible"
    nzTitle="Edit User"
    (nzOnCancel)="handleEditCancel()"
    (nzOnOk)="handleEditOk()"
    [nzOkLoading]="isModalLoading"
    nzOkText="Save"
  >
  <ng-container *nzModalContent>
    <form [formGroup]="editForm" class="modern-form">
      <div class="form-item">
        <label>Full Name</label>
        <input nz-input formControlName="fullName" placeholder="Full Name" />
        <div *ngIf="editForm.get('fullName')?.invalid && (editForm.get('fullName')?.dirty || editForm.get('fullName')?.touched || submitForm)" class="error-message">
          <span *ngIf="editForm.get('fullName')?.errors?.['required']">Full name is required</span>
          <span *ngIf="editForm.get('fullName')?.errors?.['minlength']">Full name must be at least 3 characters</span>
        </div>
      </div>

      <div class="form-item">
        <label>Username</label>
        <input nz-input formControlName="username" placeholder="Username" />
        <div *ngIf="editForm.get('username')?.invalid && (editForm.get('username')?.dirty || editForm.get('username')?.touched || submitForm)" class="error-message">
          <span *ngIf="editForm.get('username')?.errors?.['required']">Username is required</span>
          <span *ngIf="editForm.get('username')?.errors?.['pattern']">Username must contain only letters, numbers, and underscores</span>
        </div>
      </div>
    </form>
  </ng-container>
  </nz-modal>

  <!-- Change Password Modal -->
  <nz-modal
    [(nzVisible)]="isPasswordModalVisible"
    nzTitle="Change Password"
    (nzOnCancel)="handlePasswordCancel()"
    (nzOnOk)="handlePasswordOk()"
    [nzOkLoading]="isModalLoading"
    nzOkText="Change"
  >
  <ng-container *nzModalContent>
    <form [formGroup]="passwordForm" class="modern-form">
      <div class="form-item">
        <label>New Password</label>
        <input nz-input type="password" formControlName="password" placeholder="New Password" />
        <div *ngIf="passwordForm.get('password')?.invalid && (passwordForm.get('password')?.dirty || passwordForm.get('password')?.touched || submitForm)" class="error-message">
          <span *ngIf="passwordForm.get('password')?.errors?.['required']">Password is required</span>
          <span *ngIf="passwordForm.get('password')?.errors?.['minlength']">Password must be at least 6 characters</span>
        </div>
      </div>
    </form>
  </ng-container>
  </nz-modal>
</div>
