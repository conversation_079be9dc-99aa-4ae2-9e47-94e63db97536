/* Modern styling for the accounts component */
.accounts-container {
  padding: 24px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}

.action-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

button[nz-button] {
  font-size: 14px;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.3s;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

.table-actions {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;

  .search-input {
    flex: 1;
    max-width: 400px;
  }

  .status-filter {
    width: 150px;
  }
}

.modern-table {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;

  ::ng-deep {
    .ant-table-thead > tr > th {
      background-color: #f5f5f5;
      font-weight: 600;
      color: #262626;
      padding: 16px;
    }

    .ant-table-tbody > tr > td {
      padding: 16px;
      transition: background-color 0.3s;
    }

    .ant-table-tbody > tr:hover > td {
      background-color: #f0f7ff;
    }

    .ant-pagination {
      margin-top: 16px;
    }
  }
}

.user-row {
  transition: all 0.3s;

  .user-name {
    font-weight: 500;
    color: #262626;
  }

  .username {
    color: #595959;
  }

  .status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 13px;
    font-weight: 500;

    i.feather {
      margin-right: 6px;
      font-size: 14px;
    }

    &.active {
      background-color: #f6ffed;
      border: 1px solid #b7eb8f;
      color: #52c41a;
    }

    &.inactive {
      background-color: #f5f5f5;
      border: 1px solid #d9d9d9;
      color: #8c8c8c;
    }
  }
}

.action-buttons {
  display: flex;
  gap: 8px;

  button {
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    width: 32px;
    height: 32px;

    &:hover {
      transform: scale(1.1);
    }

    i.feather {
      font-size: 16px;
    }

    .active-icon {
      color: #52c41a;
    }

    .inactive-icon {
      color: #ff4d4f;
    }
  }
}

/* Modern form styling */
.modern-form {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .form-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  label {
    font-weight: 500;
    color: #262626;
    font-size: 14px;
  }

  input[nz-input],
  nz-select {
    border-radius: 6px;
    padding: 10px 12px;
    border: 1px solid #d9d9d9;
    transition: all 0.3s;

    &:hover {
      border-color: #40a9ff;
    }

    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  .error-message {
    color: #ff4d4f;
    font-size: 12px;
    margin-top: 4px;
  }
}
