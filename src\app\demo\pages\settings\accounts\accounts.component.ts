import { CommonModule } from '@angular/common';
import { Component, OnInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { FormsModule } from '@angular/forms';
import { SharedModule } from 'src/app/theme/shared/shared.module';
import { AccountsService } from './accounts.service';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzTagModule } from 'ng-zorro-antd/tag';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import ValidatePermissionComponent from "../../../../theme/shared/components/validate-permission/validate-permission.component";

export interface User {
  id: number | string; // Adjust type as needed
  fullName: string;
  username: string;
  status: 0 | 1; // 0: Inactive, 1: Active
  createdAt: string | Date;
  updatedAt: string | Date;
}

// You might also want interfaces for payload types
export interface CreateUserPayload {
  fullName: string;
  username: string;
  status: 0 | 1;
  password?: string; // Add password if created here
}

export interface UpdateUserPayload {
  fullName: string;
  username: string;
  status: 0 | 1;
}

export interface ChangePasswordPayload {
  userId: number | string;
  newPassword: string; // Required for API call
}

@Component({
  selector: 'app-accounts',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    SharedModule,
    NzInputModule,
    NzTagModule,
    NzToolTipModule,
    ValidatePermissionComponent
],
  templateUrl: './accounts.component.html',
  styleUrls: ['./accounts.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush // Use OnPush for better performance
})
export default class AccountsComponent implements OnInit {
  users: User[] = [];
  filteredUsers: User[] = [];
  isTableLoading = false;
  isModalLoading = false;
  selectedUser: User | null = null;
  isPageAllowed:any;
  // Search and filter
  searchText = '';
  statusFilter = 'all';

  // Modal Visibility Flags
  isCreateModalVisible = false;
  isEditModalVisible = false;
  isPasswordModalVisible = false;

  // Separate Forms
  createForm!: FormGroup;
  editForm!: FormGroup;
  passwordForm!: FormGroup;

  // Form validation states
  submitForm = false;

  constructor(
    private accountsService: AccountsService,
    private modal: NzModalService,
    private fb: FormBuilder,
    private cdr: ChangeDetectorRef, // Inject ChangeDetectorRef for OnPush
    private message: NzMessageService
  ) {}

  ngOnInit(): void {
    this.initializeForms();
    this.loadUsers();
  }

  initializeForms(): void {
    // Form for Creating Users (include password here if needed)
    this.createForm = this.fb.group({
      fullName: ['', [Validators.required, Validators.minLength(3)]],
      username: ['', [Validators.required, Validators.pattern(/^[a-zA-Z0-9_]+$/)]], // Example pattern: alphanumeric + underscore
      password: ['', [Validators.required, Validators.minLength(6)]], // Add password to create form
      status: [1, Validators.required] // Default to Active
    });

    // Form for Editing Users (password is handled separately)
    this.editForm = this.fb.group({
      fullName: ['', [Validators.required, Validators.minLength(3)]],
      username: ['', [Validators.required, Validators.pattern(/^[a-zA-Z0-9_]+$/)]],
      status: [1, Validators.required]
    });

    // Form for Changing Password
    this.passwordForm = this.fb.group({
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  loadUsers(): void {
    this.isTableLoading = true;
    this.accountsService.getUsers().subscribe({
      next: (response: any) => { // Use specific type if known e.g. { data: User[] }
        this.users = response.data || []; // Adjust based on actual API response structure
        this.filteredUsers = [...this.users]; // Initialize filtered users
        this.isTableLoading = false;
        this.cdr.markForCheck(); // Trigger change detection for OnPush
      },
      error: (err) => {
        console.error('Failed to load users:', err);
        this.isTableLoading = false;
        this.message.error('Failed to load users. Please try again.');
        this.cdr.markForCheck();
      }
    });
  }

  filterUsers(): void {
    this.filteredUsers = this.users.filter(user => {
      // Filter by search text
      const matchesSearch = !this.searchText ||
        user.fullName.toLowerCase().includes(this.searchText.toLowerCase()) ||
        user.username.toLowerCase().includes(this.searchText.toLowerCase());

      // Filter by status
      const matchesStatus = this.statusFilter === 'all' ||
        user.status.toString() === this.statusFilter;

      return matchesSearch && matchesStatus;
    });

    this.cdr.markForCheck();
  }

  // --- Modal Operations ---

  openCreateModal(): void {
    // Reset form with default values for all fields
    this.createForm.reset({
      fullName: '',
      username: '',
      password: '',
      status: 1
    });
    this.isCreateModalVisible = true;
     this.cdr.markForCheck(); // Ensure UI updates
  }

  openEditModal(user: User): void {
    this.selectedUser = user;
    // Patch only the fields present in the editForm
    this.editForm.patchValue({
        fullName: user.fullName,
        username: user.username,
        status: user.status
    });
    this.isEditModalVisible = true;
  }

  openPasswordModal(user: User): void {
    this.selectedUser = user;
    this.passwordForm.reset();
    this.isPasswordModalVisible = true;
  }

  // --- Form Handlers ---

  handleCreateCancel(): void {
    this.isCreateModalVisible = false;
  }

  handleCreateOk(): void {
    // Trigger validation and display errors if any
    for (const i in this.createForm.controls) {
        if (this.createForm.controls.hasOwnProperty(i)) {
            this.createForm.controls[i].markAsDirty();
            this.createForm.controls[i].updateValueAndValidity();
        }
    }

    if (this.createForm.invalid) {
        // this.message.warning('Please fill in all required fields correctly.');
        return;
    }

    this.isModalLoading = true;
    const payload: CreateUserPayload = this.createForm.value; // Type casting

    this.accountsService.createUser(payload).subscribe({
      next: () => {
        // this.message.success('User created successfully!');
        this.isCreateModalVisible = false;
        this.loadUsers(); // Reload list
      },
      error: (err) => {
        console.error('Failed to create user:', err);
        // this.message.error(err.error?.message || 'Failed to create user.'); // Display server error if available
      },
      complete: () => {
        this.isModalLoading = false;
        this.cdr.markForCheck();
      }
    });
  }

  handleEditCancel(): void {
    this.isEditModalVisible = false;
    this.selectedUser = null; // Clear selected user on cancel
  }

  handleEditOk(): void {
     // Trigger validation
    for (const i in this.editForm.controls) {
        if (this.editForm.controls.hasOwnProperty(i)) {
            this.editForm.controls[i].markAsDirty();
            this.editForm.controls[i].updateValueAndValidity();
        }
    }

    if (!this.selectedUser || this.editForm.invalid) {
      // this.message.warning('Please correct the errors in the form.');
      return;
    }

    this.isModalLoading = true;
    const payload: UpdateUserPayload = this.editForm.value;

    this.accountsService.updateUser(this.selectedUser.id, payload).subscribe({
      next: () => {
        // this.message.success('User updated successfully!');
        this.isEditModalVisible = false;
        this.selectedUser = null;
        this.loadUsers();
      },
      error: (err) => {
        console.error('Failed to update user:', err);
        // this.message.error(err.error?.message || 'Failed to update user.');
      },
      complete: () => {
        this.isModalLoading = false;
        this.cdr.markForCheck();
      }
    });
  }

  handlePasswordCancel(): void {
    this.isPasswordModalVisible = false;
    this.selectedUser = null;
  }

  handlePasswordOk(): void {
     // Trigger validation
    for (const i in this.passwordForm.controls) {
        if (this.passwordForm.controls.hasOwnProperty(i)) {
            this.passwordForm.controls[i].markAsDirty();
            this.passwordForm.controls[i].updateValueAndValidity();
        }
    }

    if (!this.selectedUser || this.passwordForm.invalid) {
      // this.message.warning('Please enter a valid password (minimum 6 characters).');
      return;
    }

     this.isModalLoading = true;
    const payload: ChangePasswordPayload = {
      userId: this.selectedUser.id,
      newPassword: this.passwordForm.value.password
    };


    this.accountsService.changePassword(payload).subscribe({
      next: () => {
        // this.message.success('Password changed successfully!');
        this.isPasswordModalVisible = false;
        this.selectedUser = null;
        this.message.success(`Password changed successfully!`);
      },
      error: (err) => {
        console.error('Failed to change password:', err);
        this.isModalLoading = false;
        this.message.error(err.error?.message || 'Failed to change password.');
      },
      complete: () => {
        this.isModalLoading = false;
        this.cdr.markForCheck();
      }
    });
  }

  // --- User Status Actions ---

  activateUser(user: User): void {
    this.modal.confirm({
        nzTitle: 'Activate User',
        nzContent: `Are you sure you want to activate the user "${user.fullName}"?`,
        nzOkText: 'Activate',
        nzOkType: 'primary',
        nzOnOk: () => this.performActivation(user.id, true)
    });
  }

  deactivateUser(user: User): void {
    this.modal.confirm({
      nzTitle: 'Deactivate User',
      nzContent: `Are you sure you want to deactivate the user "${user.fullName}"? This might restrict their access.`,
      nzOkText: 'Deactivate',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzOnOk: () => this.performActivation(user.id, false)
    });
  }

  private performActivation(userId: number | string, activate: boolean): void {
    const action = activate ? this.accountsService.activateUser(userId) : this.accountsService.deactivateUser(userId);
    const messageVerb = activate ? 'activated' : 'deactivated';

    // Consider a temporary loading state for the specific row if needed
    // For simplicity, we'll just show a general message

    action.subscribe({
        next: () => {
            this.message.success(`User ${messageVerb} successfully!`);
            this.loadUsers(); // Refresh the list
        },
        error: (err) => {
            console.error(`Failed to ${messageVerb} user:`, err);
            this.message.error(err.error?.message || `Failed to ${messageVerb} user.`);
            this.cdr.markForCheck(); // Ensure UI updates on error if needed
        }
    });
  }


}
