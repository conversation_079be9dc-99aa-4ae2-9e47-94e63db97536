import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { StorageService } from 'src/app/theme/shared/service/storage.service';

export interface User {
  id: number;
  fullName: string;
  username: string;
  status: number;
  createdAt: string;
  updatedAt: string;
}

@Injectable({
  providedIn: 'root'
})
export class AccountsService {
  private apiUrl = environment.URL;

  constructor(private http: HttpClient, private storageService: StorageService) { }
  private getHttpOptions() {
    const token = this.storageService.getToken();
    return {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      })
    };
  }
  getUsers(): Observable<User[]> {
    return this.http.get<User[]>(`${this.apiUrl}/users`, this.getHttpOptions());
  }

  createUser(data: any): Observable<any> {
    return this.http.post(`${this.apiUrl}/createaccount`, data, this.getHttpOptions());
  }

  updateUser(id: number | string, data: any): Observable<any> {
    return this.http.put(`${this.apiUrl}/users/${id}`, data, this.getHttpOptions());
  }

  activateUser(id: number | string): Observable<any> {
    return this.http.put(`${this.apiUrl}/users/active/${id}`, {}, this.getHttpOptions());
  }

  deactivateUser(id: number | string): Observable<any> {
    return this.http.put(`${this.apiUrl}/users/deactive/${id}`, {}, this.getHttpOptions());
  }

  changePassword(data: { userId: number | string, newPassword: string }): Observable<any> {
    return this.http.put(`${this.apiUrl}/password/resets`, data, this.getHttpOptions());
  }
}
