/**
 * Helper function to generate Feather icon SVG markup
 * @param name - The name of the Feather icon
 * @param size - The size of the icon (default: 24)
 * @param strokeWidth - The stroke width (default: 2)
 * @param color - The stroke color (default: 'currentColor')
 * @returns SVG markup for the Feather icon
 */
export function featherIcon(
  name: string,
  size: number = 24,
  strokeWidth: number = 2,
  color: string = 'currentColor'
): string {
  // Base URL for Feather icons
  const baseUrl = 'https://feathericons.dev/?icon=';
  
  // Create the URL with parameters
  const url = `${baseUrl}${name}&size=${size}&stroke-width=${strokeWidth}&color=${encodeURIComponent(color)}`;
  
  // Return the SVG markup
  return `<img src="${url}" alt="${name} icon" width="${size}" height="${size}" />`;
}
