<app-validate-permission  [pageId]="'permissions'" (permissionChecked)="isPageAllowed = $event"></app-validate-permission>
<div *ngIf="isPageAllowed" class="permission-settings-container">
  <div class="row">
    <div class="col-md-12">
      <app-card cardTitle="User Permissions Management" blockClass="p-0">
        <div class="row">
          <!-- User Selection Section -->
          <div class="col-md-4">
            <div class="modern-card">
              <div class="card-header">
                <h5>Users</h5>
                <div class="search-container mt-2">
                  <nz-input-group [nzSuffix]="suffixIconSearch">
                    <input type="text" nz-input [(ngModel)]="searchText" placeholder="Search users..." (input)="onSearch()" />
                  </nz-input-group>
                  <ng-template #suffixIconSearch>
                    <i nz-icon nzType="search"></i>
                  </ng-template>
                </div>
              </div>
              <div class="card-body">
                <!-- Loading state -->
                <div *ngIf="loading" class="loading-spinner">
                  <div class="spinner"></div>
                </div>

                <!-- User list -->
                <div class="user-list" *ngIf="!loading">
                  <div *ngFor="let user of filteredUsers"
                       class="user-item"
                       [class.active]="selectedUser && selectedUser.id === user.id"
                       (click)="selectUser(user)">
                    <div class="d-flex align-items-center">
                      <div class="user-avatar">
                        <i class="feather icon-user"></i>
                      </div>
                      <div>
                        <div class="user-name">{{ user.fullName }}</div>
                        <div class="user-email" *ngIf="user.email">{{ user.email }}</div>
                      </div>
                    </div>
                  </div>
                  <!-- Empty state -->
                  <div *ngIf="filteredUsers.length === 0" class="empty-state">
                    <i class="feather icon-users"></i>
                    <p>No users found</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Permissions Section -->
          <div class="col-md-8">
            <div class="modern-card">
              <div class="card-header">
                <h5 *ngIf="selectedUser">Permissions for {{ selectedUser.username }}</h5>
                <h5 *ngIf="!selectedUser">Select a user to manage permissions</h5>
              </div>
              <div class="card-body">
                <!-- Loading state -->
                <div *ngIf="loading" class="loading-spinner">
                  <div class="spinner"></div>
                </div>

                <!-- Permissions content -->
                <div *ngIf="!loading && selectedUser">
                  <p class="mb-3">Select the menu items this user should have access to:</p>
                  <!-- <p class="text-muted small mb-3">Note: You can only modify permissions for menu items that you have access to.</p> -->

                  <!-- Warning alert -->
                  <!-- <div *ngIf="selectedUser.id === currentUserId" class="modern-alert warning">
                    <i class="feather icon-alert-triangle"></i>
                    <div class="alert-content">
                      You cannot modify your own permissions. Please ask another administrator to make changes to your account.
                    </div>
                  </div> -->

                  <!-- Group permissions by parent -->
                  <div class="menu-items-container">
                    <!-- Group permissions that don't have a  [class.disabled-permission]="!canModifyMenuItem(menuItem.id)"  [disabled]="!canModifyMenuItem(menuItem.id)"  [disabled]="!canModifyMenuItem(menuItem.id)" -->
                    <div class="permission-category">
                      <div class="category-title">Main Menu Items</div>
                      <div class="row">
                        <div *ngFor="let menuItem of menuItems | filterBy: ['parent']: undefined" class="col-md-6">
                          <div class="permission-item">
                            <div class="modern-checkbox" >
                              <input class="form-check-input" type="checkbox"
                                    [id]="'menu-' + menuItem.id"
                                    [checked]="isMenuItemSelected(menuItem.id)"

                                    (change)="toggleMenuItem(menuItem.id)">
                              <label class="form-check-label" [for]="'menu-' + menuItem.id"
                                    >
                                <i *ngIf="menuItem.icon" class="feather {{ menuItem.icon }}"></i>
                                {{ menuItem.title }}
                              </label>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Group permissions by parent -->
                    <ng-container *ngFor="let parent of getUniqueParents()">
                      <div class="permission-category" *ngIf="parent">
                        <div class="category-title">{{ parent }}</div>
                        <div class="row">
                          <div *ngFor="let menuItem of menuItems | filterBy: ['parent']: parent" class="col-md-6">
                            <div class="permission-item">
                              <div class="modern-checkbox" >
                                <input class="form-check-input" type="checkbox"
                                      [id]="'menu-' + menuItem.id"
                                      [checked]="isMenuItemSelected(menuItem.id)"

                                      (change)="toggleMenuItem(menuItem.id)">
                                <label class="form-check-label" [for]="'menu-' + menuItem.id"
                                     >
                                  <i *ngIf="menuItem.icon" class="feather {{ menuItem.icon }}"></i>
                                  {{ menuItem.title }}
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </ng-container>
                  </div>

                  <!-- Action buttons -->
                  <div class="mt-4 text-end">
                    <button nz-button nzType="primary"
                            class="action-button primary"
                            (click)="savePermissions()"
                            [disabled]="loading || selectedUser.id === currentUserId">
                      <i class="feather icon-save"></i> Save Permissions
                    </button>
                  </div>
                </div>

                <!-- Empty state -->
                <div *ngIf="!loading && !selectedUser" class="empty-state">
                  <i class="feather icon-user-x"></i>
                  <p>Please select a user from the list to manage their permissions</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </app-card>
    </div>
  </div>
</div>
