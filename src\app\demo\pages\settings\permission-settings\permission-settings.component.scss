// Modern styling for permission settings
.permission-settings-container {
  padding: 20px;
}

// Card styling
.modern-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 6px 16px -8px rgba(0, 0, 0, 0.08),
              0 9px 28px 0 rgba(0, 0, 0, 0.05),
              0 12px 48px 16px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
  border: none;
  overflow: hidden;

  .card-header {
    background: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    padding: 16px 24px;

    h5 {
      margin: 0;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
    }
  }

  .card-body {
    padding: 24px;
  }
}

// User list styling
.user-list {
  max-height: 500px;
  overflow-y: auto;
  padding: 0;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

.user-item {
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  margin-bottom: 12px;
  border-radius: 8px;
  padding: 14px 16px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  &:hover {
    background-color: #f9fbff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  &.active {
    background-color: #f0f7ff;
    color: #1890ff;
    border-left: 3px solid #1890ff;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);

    .user-email {
      color: rgba(24, 144, 255, 0.7);
    }

    .user-avatar {
      background-color: rgba(24, 144, 255, 0.2);

      i {
        color: #1890ff;
      }
    }
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.user-name {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 2px;
}

.user-email {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  margin-right: 12px;

  i {
    font-size: 18px;
  }
}

// Search styling
.search-container {
  margin-bottom: 20px;

  ::ng-deep .ant-input-affix-wrapper {
    border-radius: 8px;
    padding: 8px 12px;

    &:hover, &:focus {
      border-color: #1890ff;
    }

    .ant-input {
      font-size: 14px;
    }
  }
}

// Permissions styling
.menu-items-container {
  border-radius: 12px;
  padding: 24px;
  background-color: #f9fafc;
  box-shadow: inset 0 0 15px rgba(0, 0, 0, 0.02);
  margin-bottom: 24px;
  position: relative;
  overflow: hidden;

  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #1890ff, #52c41a);
    opacity: 0.7;
  }
}

.permission-category {
  margin-bottom: 24px;

  .category-title {
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  }
}

.permission-item {
  padding: 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
  margin-bottom: 8px;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

  &:hover {
    background-color: #f9fbff;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  }
}

// Checkbox styling
.modern-checkbox {
  .form-check-input {
    width: 18px;
    height: 18px;
    margin-top: 0.2rem;
    border: 2px solid rgba(0, 0, 0, 0.15);
    border-radius: 4px;

    &:checked {
      background-color: #1890ff;
      border-color: #1890ff;
    }

    &:focus {
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }
  }

  .form-check-label {
    padding-left: 8px;
    font-size: 14px;

    i {
      margin-right: 6px;
      color: rgba(0, 0, 0, 0.45);
    }
  }
}

// Disabled permissions
.disabled-permission {
  opacity: 0.6;
  cursor: not-allowed;

  .form-check-input {
    cursor: not-allowed;
  }

  .form-check-label {
    cursor: not-allowed;
  }
}

// Button styling
.action-button {
  border-radius: 6px;
  padding: 8px 16px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;

  i {
    margin-right: 8px;
  }

  &.primary {
    background-color: #1890ff;
    border-color: #1890ff;
    color: white;

    &:hover {
      background-color: #40a9ff;
      border-color: #40a9ff;
    }

    &:active {
      background-color: #096dd9;
      border-color: #096dd9;
    }
  }
}

// Empty state styling
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;

  i {
    font-size: 48px;
    color: rgba(0, 0, 0, 0.15);
    margin-bottom: 16px;
  }

  p {
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
  }
}

// Alert styling
.modern-alert {
  border-radius: 8px;
  padding: 12px 16px;
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;

  &.warning {
    background-color: #fffbe6;
    border: 1px solid #ffe58f;

    i {
      color: #faad14;
    }
  }

  i {
    margin-right: 12px;
    font-size: 16px;
    margin-top: 2px;
  }

  .alert-content {
    flex: 1;
    font-size: 14px;
  }
}

// Loading spinner
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;

  .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(24, 144, 255, 0.1);
    border-radius: 50%;
    border-top-color: #1890ff;
    animation: spin 1s ease-in-out infinite;
    position: relative;

    &:before, &:after {
      content: '';
      position: absolute;
      top: -3px;
      left: -3px;
      width: 100%;
      height: 100%;
      border: 3px solid transparent;
      border-radius: 50%;
      border-top-color: rgba(24, 144, 255, 0.3);
      animation: spin 2s linear infinite;
    }

    &:after {
      border-top-color: rgba(24, 144, 255, 0.15);
      animation: spin 3s linear infinite;
    }
  }
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

// Responsive adjustments
@media (max-width: 992px) {
  .row {
    flex-direction: column;

    .col-md-4, .col-md-8 {
      width: 100%;
      max-width: 100%;
      flex: 0 0 100%;
    }

    .col-md-4 {
      margin-bottom: 20px;
    }
  }

  .permission-category {
    .row {
      flex-direction: row;
    }
  }
}

@media (max-width: 576px) {
  .permission-category {
    .row {
      .col-md-6 {
        width: 100%;
      }
    }
  }

  .action-button {
    width: 100%;
  }
}
