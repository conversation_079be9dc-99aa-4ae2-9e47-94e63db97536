import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { SharedModule } from 'src/app/theme/shared/shared.module';

import { StorageService } from 'src/app/theme/shared/service/storage.service';
import { NavigationService } from 'src/app/theme/shared/service/navigation.service';
import { NavigationFilterNewService } from 'src/app/theme/shared/service/navigation-filter-new.service';
import { NavigationItems } from 'src/app/theme/layout/admin/navigation/navigation';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzPopconfirmModule } from 'ng-zorro-antd/popconfirm';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTableModule } from 'ng-zorro-antd/table';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { firstValueFrom } from 'rxjs';
import { FilterByPipe } from 'src/app/theme/shared/pipes/filter-by.pipe';
import PermissionService from 'src/app/theme/shared/service/permission.service';
import ValidatePermissionComponent from "../../../../theme/shared/components/validate-permission/validate-permission.component";

declare var cuteAlert: any;

@Component({
  selector: 'app-permission-settings',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    SharedModule,
    NzInputModule,
    NzButtonModule,
    NzIconModule,
    NzPopconfirmModule,
    NzSelectModule,
    NzTableModule,
    FilterByPipe,
    ValidatePermissionComponent
],
  providers: [PermissionService],
  templateUrl: './permission-settings.component.html',
  styleUrls: ['./permission-settings.component.scss']
})
export default class PermissionSettingsComponent implements OnInit {
  // Data properties
  users: any[] = [];
  permissions: any[] = [];
  menuItems: any[] = [];
  selectedUser: any = null;
  selectedMenuItems: string[] = [];
  loading = false;

  // Search and filter properties
  searchText: string = '';
  filteredUsers: any[] = [];

  // Current user properties
  currentUserId: number;
  currentUserPermissions: string[] = [];

  // Token for API calls
  token = this.storageService.getToken();

  constructor(
    private permissionService: PermissionService,
    private storageService: StorageService,
    private navigationService: NavigationService,
    private navigationFilterService: NavigationFilterNewService,
    private http: HttpClient
  ) {
    // Get current user ID
    const userIdStr = this.storageService.getUserId();
    this.currentUserId = userIdStr ? Number(userIdStr) : 0;

    // Get current user permissions
    this.currentUserPermissions = this.navigationFilterService.getUserPermissions();
  }
  isPageAllowed:any;

  ngOnInit(): void {
    this.loadUsers();
    this.extractMenuItems();
  }

  // Extract menu items from navigation
  extractMenuItems(): void {
    this.menuItems = [];

    // Process navigation items to extract menu IDs
    NavigationItems.forEach(group => {
      if (group.children) {
        group.children.forEach(item => {
          if (item.type === 'item') {
            this.menuItems.push({
              id: item.id,
              title: item.title,
              icon: item.icon || ''
            });
          } else if (item.type === 'collapse' && item.children) {
            item.children.forEach(subItem => {
              this.menuItems.push({
                id: subItem.id,
                title: subItem.title,
                icon: subItem.icon || '',
                parent: item.title
              });
            });
          }
        });
      }
    });
  }

  // Load all users
  loadUsers(): void {
    this.loading = true;

    // Get users from API
    const httpOptions = {
      headers: {
        'Authorization': `Bearer ${this.token}`
      }
    };

    this.http.get<any>(`${environment.URL}/users`, httpOptions).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.users = response.data;
          this.filteredUsers = [...this.users];
        } else {
          // If no users returned, create a dummy user for testing
          // This is helpful during initial setup when the API might not be fully ready
          this.users = [
            { id: 1, username: 'admin', email: '<EMAIL>' }
          ];
          this.filteredUsers = [...this.users];
          console.log('No users returned from API, using dummy user for testing');
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading users:', error);
        // Create dummy user on error for testing purposes
        this.users = [
          { id: 1, username: 'admin', email: '<EMAIL>' }
        ];
        this.filteredUsers = [...this.users];
        this.loading = false;
        console.log('Error loading users, using dummy user for testing');
      }
    });
  }

  // Load permissions for a specific user
  loadUserPermissions(userId: number): void {
    this.loading = true;
    this.selectedMenuItems = [];

    this.permissionService.getPermissionsByUserId(userId).subscribe({
      next: (response) => {
        if (response && response.data) {
          this.permissions = response.data;
          // Extract menu IDs from permissions and remove duplicates
          const menuIds = this.permissions.map(p => p.nav_menuId);
          this.selectedMenuItems = [...new Set(menuIds)];
        } else {
          this.permissions = [];
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading permissions:', error);
        this.permissions = [];
        this.loading = false;
        this.showAlert('error', 'Error', 'Failed to load permissions');
      }
    });
  }

  // Select a user to manage permissions
  selectUser(user: any): void {
    this.selectedUser = user;
    this.loadUserPermissions(user.id);
  }

  // Save permissions for the selected user
  savePermissions(): void {
    if (!this.selectedUser) {
      this.showAlert('warning', 'Warning', 'Please select a user first');
      return;
    }

    // Prevent users from modifying their own permissions
    if (this.selectedUser.id === this.currentUserId) {
      this.showAlert('error', 'Error', 'You cannot modify your own permissions');
      return;
    }

    this.loading = true;

    // Remove duplicates from the selectedMenuItems array using Set
    const uniqueMenuItems = [...new Set(this.selectedMenuItems)];

    // Create data object for API call
    const data = {
      userId: this.selectedUser.id,
      Menu: uniqueMenuItems
    };

    console.log('Saving permissions:', data);

    // Get current permissions for the user
    this.permissionService.getPermissionsByUserId(this.selectedUser.id).subscribe({
      next: (response) => {
        if (response && response.data) {
          const currentPermissions = response.data;
          const currentMenuIds = currentPermissions.map((p: any) => p.nav_menuId);
          const newMenuIds = uniqueMenuItems;

          // Find permissions to delete (in current but not in new)
          const toDelete = currentPermissions.filter((p: any) => !newMenuIds.includes(p.nav_menuId));

          // Find permissions to add (in new but not in current)
          const toAdd = newMenuIds.filter((menuId: string) => !currentMenuIds.includes(menuId));

          console.log('Permissions to delete:', toDelete);
          console.log('Permissions to add:', toAdd);

          // Create an array of promises for all delete operations
          const deletePromises = toDelete.map((p: any) =>
            firstValueFrom(this.permissionService.deletePermission(p.id))
          );

          // Execute all delete operations
          Promise.all(deletePromises)
            .then(() => {
              console.log('All permissions deleted successfully');

              // If there are permissions to add
              if (toAdd.length > 0) {
                // Create data for adding new permissions
                const addData = {
                  userId: this.selectedUser.id,
                  Menu: toAdd
                };

                // Add new permissions
                this.permissionService.createMultiplePermissions(addData).subscribe({
                  next: (response) => {
                    this.loading = false;
                    console.log('Permissions added successfully:', response);
                    this.showAlert('success', 'Success', 'Permissions updated successfully');

                    // Refresh navigation to reflect permission changes
                    this.navigationService.refreshNavigation();

                    // Reload permissions to get updated data
                    this.loadUserPermissions(this.selectedUser.id);
                  },
                  error: (error) => {
                    console.error('Error adding permissions:', error);
                    this.loading = false;
                    this.navigationService.refreshNavigation();
                    this.showAlert('warning', 'Warning', 'Failed to add some permissions. Please try again later.');
                  }
                });
              } else {
                // No permissions to add, just finish
                this.loading = false;
                this.showAlert('success', 'Success', 'Permissions updated successfully');

                // Refresh navigation to reflect permission changes
                this.navigationService.refreshNavigation();

                // Reload permissions to get updated data
                this.loadUserPermissions(this.selectedUser.id);
              }
            })
            .catch((error) => {
              console.error('Error deleting permissions:', error);
              this.loading = false;
              this.showAlert('warning', 'Warning', 'Failed to update some permissions. Please try again later.');
            });
        } else {
          // No current permissions, just add all new ones
          this.permissionService.createMultiplePermissions(data).subscribe({
            next: (response) => {
              this.loading = false;
              console.log('Permissions saved successfully:', response);
              this.showAlert('success', 'Success', 'Permissions saved successfully');

              // Refresh navigation to reflect permission changes
              this.navigationService.refreshNavigation();

              // Reload permissions to get updated data
              this.loadUserPermissions(this.selectedUser.id);
            },
            error: (error) => {
              console.error('Error saving permissions:', error);
              this.loading = false;
              this.navigationService.refreshNavigation();
              this.showAlert('warning', 'Warning', 'Failed to save permissions. Please try again later.');
            }
          });
        }
      },
      error: (error) => {
        console.error('Error getting current permissions:', error);
        this.loading = false;
        this.showAlert('warning', 'Warning', 'Failed to update permissions. Please try again later.');
      }
    });
  }

  // Search users
  onSearch(): void {
    if (!this.searchText) {
      this.filteredUsers = [...this.users];
      return;
    }

    const searchTerm = this.searchText.toLowerCase();
    this.filteredUsers = this.users.filter(user =>
      user.username.toLowerCase().includes(searchTerm) ||
      (user.email && user.email.toLowerCase().includes(searchTerm))
    );
  }

  // Check if a menu item is selected
  isMenuItemSelected(menuId: string): boolean {
    return this.selectedMenuItems.includes(menuId);
  }

  // Check if current user has permission to modify a menu item


  // Toggle menu item selection
  toggleMenuItem(menuId: string): void {

    const index = this.selectedMenuItems.indexOf(menuId);
    if (index === -1) {
      this.selectedMenuItems.push(menuId);
    } else {
      this.selectedMenuItems.splice(index, 1);
    }
  }

  // Show alert using cuteAlert
  showAlert(type: string, title: string, message: string): void {
    cuteAlert({
      type: type,
      title: title,
      message: message,
      buttonText: 'OK'
    });
  }

  // Get unique parent categories for grouping permissions
  getUniqueParents(): string[] {
    if (!this.menuItems || this.menuItems.length === 0) {
      return [];
    }

    // Extract all parent values and filter out undefined/null values
    const parents = this.menuItems
      .map(item => item.parent)
      .filter(parent => parent !== undefined && parent !== null);

    // Remove duplicates using Set
    return [...new Set(parents)];
  }
}
