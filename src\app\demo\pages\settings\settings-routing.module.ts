import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    children: [
      {
        path: 'permissions',
        loadComponent: () => import('./permission-settings/permission-settings.component')
      },
      {
        path: 'accounts',
        loadComponent: () => import('./accounts/accounts.component')
      },
      {
        path: 'change-password',
        loadComponent: () => import('./users/change-password/change-password.component')
      },
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SettingsRoutingModule { }
