<div class="change-password-container">
  <div class="card">
    <div class="card-header">
      <h2>Change Password</h2>
    </div>
    <div class="card-body">
      <form [formGroup]="changePasswordForm" (ngSubmit)="onSubmit()" novalidate>

        <!-- Current Password -->
        <div class="form-group">
          <label for="currentPassword">Current Password</label>
          <input type="password"
                 id="currentPassword"
                 formControlName="currentPassword"
                 placeholder="Enter your current password"
                 [ngClass]="{'is-invalid': currentPassword?.invalid && (currentPassword?.dirty || currentPassword?.touched)}">
          <div *ngIf="currentPassword?.invalid && (currentPassword?.dirty || currentPassword?.touched)" class="invalid-feedback">
            <div *ngIf="currentPassword?.errors?.['required']">Current password is required.</div>
          </div>
        </div>

        <!-- New Password -->
        <div class="form-group">
          <label for="newPassword">New Password</label>
          <input type="password"
                 id="newPassword"
                 formControlName="newPassword"
                 placeholder="Enter your new password"
                 [ngClass]="{'is-invalid': newPassword?.invalid && (newPassword?.dirty || newPassword?.touched)}">
          <div *ngIf="newPassword?.invalid && (newPassword?.dirty || newPassword?.touched)" class="invalid-feedback">
            <div *ngIf="newPassword?.errors?.['required']">New password is required.</div>
            <div *ngIf="newPassword?.errors?.['minlength']">Password must be at least 8 characters long.</div>
            <!-- Add more complex password validation messages here if needed -->
          </div>
        </div>

        <!-- Confirm New Password -->
        <div class="form-group">
          <label for="confirmPassword">Confirm New Password</label>
          <input type="password"
                 id="confirmPassword"
                 formControlName="confirmPassword"
                 placeholder="Confirm your new password"
                 [ngClass]="{'is-invalid': (confirmPassword?.invalid || changePasswordForm.errors?.['mismatch']) && (confirmPassword?.dirty || confirmPassword?.touched)}">
          <div *ngIf="(confirmPassword?.invalid || changePasswordForm.errors?.['mismatch']) && (confirmPassword?.dirty || confirmPassword?.touched)" class="invalid-feedback">
             <div *ngIf="confirmPassword?.errors?.['required'] && !confirmPassword?.errors?.['mismatch']">Please confirm your new password.</div>
             <div *ngIf="confirmPassword?.errors?.['mismatch']">Passwords do not match.</div>
             <!-- Check group error as fallback if needed: -->
             <!-- <div *ngIf="changePasswordForm.errors?.['mismatch'] && !confirmPassword?.errors?.['required']">Passwords do not match.</div> -->
          </div>
        </div>

        <!-- Submit Button -->
        <button type="submit" class="btn btn-primary" [disabled]="isLoading || changePasswordForm.invalid">
          <span *ngIf="!isLoading">Change Password</span>
          <span *ngIf="isLoading" class="spinner"></span>
          <span *ngIf="isLoading"> Changing...</span>
        </button>

        <!-- Success Message -->
        <div *ngIf="successMessage" class="alert alert-success">
          {{ successMessage }}
        </div>

        <!-- Error Message -->
        <div *ngIf="errorMessage" class="alert alert-danger">
          {{ errorMessage }}
        </div>

      </form>
    </div>
  </div>
</div>
