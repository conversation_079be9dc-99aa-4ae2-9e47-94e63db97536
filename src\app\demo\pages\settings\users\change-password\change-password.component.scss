// --- Variable Definitions (Customize as needed) ---
:host {
  --primary-color: #007bff;
  --primary-color-hover: #0056b3;
  --danger-color: #dc3545;
  --success-color: #28a745;
  --text-color: #333;
  --label-color: #555;
  --input-border-color: #ced4da;
  --input-focus-border-color: #80bdff;
  --input-invalid-border-color: var(--danger-color);
  --card-bg: #ffffff;
  --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --border-radius: 8px;
  --spacing-unit: 1rem; // Base spacing unit (e.g., 16px)
}

.change-password-container {
  display: flex;
  justify-content: center;
  align-items: flex-start; // Align to top if container has height
  padding: calc(var(--spacing-unit) * 2);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; // Modern font stack
}

.card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  width: 100%;
  max-width: 500px; // Limit card width
  overflow: hidden; // Ensure content stays within rounded corners
}

.card-header {
  background-color: var(--card-bg);
  color: white;
  padding: var(--spacing-unit) calc(var(--spacing-unit) * 1.5);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

  h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 500;
  }
}

.card-body {
  padding: calc(var(--spacing-unit) * 1.5);
}

form {
  display: flex;
  flex-direction: column;
  gap: calc(var(--spacing-unit) * 1.25); // Space between form elements
}

.form-group {
  display: flex;
  flex-direction: column;

  label {
    margin-bottom: calc(var(--spacing-unit) * 0.4);
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--label-color);
  }

  input[type="password"] {
    padding: calc(var(--spacing-unit) * 0.6) calc(var(--spacing-unit) * 0.8);
    border: 1px solid var(--input-border-color);
    border-radius: calc(var(--border-radius) / 2);
    font-size: 1rem;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    width: 100%; // Make input take full width of its container
    box-sizing: border-box; // Include padding and border in the element's total width and height

    &:focus {
      outline: none;
      border-color: var(--input-focus-border-color);
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    // Style for invalid inputs
    &.is-invalid {
      border-color: var(--input-invalid-border-color);

      &:focus {
         box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
      }
    }
  }
}

.invalid-feedback {
  color: var(--danger-color);
  font-size: 0.8rem;
  margin-top: calc(var(--spacing-unit) * 0.25);
}

.btn {
  padding: calc(var(--spacing-unit) * 0.7) calc(var(--spacing-unit) * 1.5);
  font-size: 1rem;
  border: none;
  border-radius: calc(var(--border-radius) / 2);
  cursor: pointer;
  transition: background-color 0.2s ease-in-out, opacity 0.2s ease-in-out;
  display: inline-flex; // Align spinner and text
  align-items: center;
  justify-content: center;
  font-weight: 500;

  &.btn-primary {
    background-color: var(--primary-color);
    color: white;

    &:hover:not(:disabled) {
      background-color: var(--primary-color-hover);
    }
  }

  &:disabled {
    opacity: 0.65;
    cursor: not-allowed;
  }
}

// Simple CSS Spinner
.spinner {
  display: inline-block;
  width: 1em; // Relative to button font size
  height: 1em;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
  margin-right: 0.5em; // Space between spinner and text
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

// Alert Styles
.alert {
  padding: calc(var(--spacing-unit) * 0.8);
  margin-top: var(--spacing-unit);
  border: 1px solid transparent;
  border-radius: calc(var(--border-radius) / 2);
  font-size: 0.95rem;

  &.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
  }

  &.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
  }
}
