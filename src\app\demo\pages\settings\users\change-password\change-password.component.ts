import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';
import { SharedModule } from 'src/app/theme/shared/shared.module';
import { ChangePasswordService } from './change-password.service';
declare var cuteAlert: any;

@Component({
  selector: 'app-change-password',
  imports:[SharedModule,CommonModule],
  templateUrl: './change-password.component.html',
  styleUrls: ['./change-password.component.scss']
})
export default class ChangePasswordComponent implements OnInit {
  changePasswordForm!: FormGroup;
  isLoading = false;
  successMessage = '';
  errorMessage = '';

  constructor(private fb: FormBuilder, private service:ChangePasswordService) {}

  ngOnInit(): void {
    this.changePasswordForm = this.fb.group({
      currentPassword: ['', [Validators.required]],
      newPassword: ['', [Validators.required, Validators.minLength(8)]], // Example: Minimum 8 characters
      confirmPassword: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator }); // Add custom validator to the group
  }

  // Custom Validator for matching passwords
  passwordMatchValidator(control: AbstractControl): ValidationErrors | null {
    const newPassword = control.get('newPassword');
    const confirmPassword = control.get('confirmPassword');

    // If controls haven't been interacted with yet, or confirmPassword has other errors, don't validate mismatch yet
    if (!newPassword || !confirmPassword || !newPassword.value || !confirmPassword.value || confirmPassword.errors && !confirmPassword.errors['mismatch']) {
      return null;
    }

    if (newPassword.value !== confirmPassword.value) {
      // Set error on the confirmPassword control
      confirmPassword.setErrors({ ...confirmPassword.errors, mismatch: true });
      return { mismatch: true }; // Optionally return error on the group as well
    } else {
       // Clear the mismatch error if it exists, *without* removing other potential errors
       const currentErrors = { ...confirmPassword.errors };
       delete currentErrors['mismatch'];
       confirmPassword.setErrors(Object.keys(currentErrors).length > 0 ? currentErrors : null);
       return null;
    }
  }

  // --- Getters for easier template access ---
  get currentPassword() { return this.changePasswordForm.get('currentPassword'); }
  get newPassword() { return this.changePasswordForm.get('newPassword'); }
  get confirmPassword() { return this.changePasswordForm.get('confirmPassword'); }
  // ---

  onSubmit(): void {
    this.successMessage = '';
    this.errorMessage = '';
    this.changePasswordForm.markAllAsTouched(); // Mark all fields as touched to show errors

    if (this.changePasswordForm.invalid) {
      console.log("Form is invalid");
      return; // Stop if form is invalid
    }

    this.isLoading = true;
    let payload = {
      currentPassword: this.changePasswordForm.value.currentPassword,
      newPassword: this.changePasswordForm.value.newPassword
    };

    if(this.changePasswordForm.value.newPassword == this.changePasswordForm.value.confirmPassword){
       this.service.changePassword(payload)
        .subscribe({
          next: (response) => {
            console.log("Password change successful:", response);
            this.successMessage = "Password changed successfully!";
            this.changePasswordForm.reset(); // Clear form on success
            // Optionally, you might want to keep the form state but clear errors:
            this.isLoading = false;
          },
          error: (error) => {
            console.error("Error changing password:", error);
            this.errorMessage = "Failed to change password.";
            this.changePasswordForm.reset();
            this.isLoading = false;
          }
        });
    }else{
      this.showAlert("Password mismatch", "error");
    }

  }

  showAlert(title: string, type: string, message: string = '') {
    cuteAlert({
      type: type,
      title: title,
      text: message,
      button: 'OK'
    });
  }




}
