import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { StorageService } from 'src/app/theme/shared/service/storage.service';



@Injectable({
  providedIn: 'root'
})
export class ChangePasswordService {
  private apiUrl = environment.URL;

  constructor(private http: HttpClient, private storageService: StorageService) { }
  private getHttpOptions() {
    const token = this.storageService.getToken();
    return {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      })
    };
  }

  changePassword(data: { currentPassword:string, newPassword: string }): Observable<any> {
    return this.http.post(`${this.apiUrl}/user/change/password`, data, this.getHttpOptions());
  }
}
