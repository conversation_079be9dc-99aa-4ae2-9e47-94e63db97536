<div class="row">
  <div class="col-sm-12">
    <app-card cardTitle="Default" [options]="false">
      <p>
        use class
        <code>.btn-*</code>
        in class
        <code>.btn</code>
        class to get various button
      </p>
      <button type="button" class="btn btn-primary">Primary</button>
      <button type="button" class="btn btn-secondary">Secondary</button>
      <button type="button" class="btn btn-success">Success</button>
      <button type="button" class="btn btn-danger">Danger</button>
      <button type="button" class="btn btn-warning">Warning</button>
      <button type="button" class="btn btn-info">Info</button>
      <button type="button" class="btn btn-dark">Dark</button>
      <button type="button" class="btn btn-link">Link</button>
    </app-card>
  </div>
  <div class="col-md-12">
    <app-card cardTitle="Outline" [options]="false">
      <p>
        use class
        <code>.btn-outline-*</code>
        in class
        <code>.btn</code>
        class to get various outline button
      </p>
      <button type="button" class="btn btn-outline-primary">Primary</button>
      <button type="button" class="btn btn-outline-secondary">Secondary</button>
      <button type="button" class="btn btn-outline-success">Success</button>
      <button type="button" class="btn btn-outline-danger">Danger</button>
      <button type="button" class="btn btn-outline-warning">Warning</button>
      <button type="button" class="btn btn-outline-info">Info</button>
      <button type="button" class="btn btn-outline-dark">Dark</button>
    </app-card>
  </div>
  <div class="col-md-12">
    <app-card cardTitle="Square Button" [options]="false">
      <p>
        use
        <code>.btn-square</code>
        in class
        <code>.btn</code>
        class to get square button
      </p>
      <button type="button" class="btn btn-square btn-primary">Primary</button>
      <button type="button" class="btn btn-square btn-secondary">Secondary</button>
      <button type="button" class="btn btn-square btn-success">Success</button>
      <button type="button" class="btn btn-square btn-danger">Danger</button>
      <button type="button" class="btn btn-square btn-warning">Warning</button>
      <button type="button" class="btn btn-square btn-info">Info</button>
      <button type="button" class="btn btn-square btn-dark">Dark</button>
    </app-card>
  </div>
  <div class="col-md-12">
    <app-card cardTitle="Disabled Button" [options]="false">
      <p>
        use
        <code>.disabled</code>
        in class
        <code>.btn</code>
        class to get Disabled button
      </p>
      <button type="button" class="btn disabled btn-primary">Primary</button>
      <button type="button" class="btn disabled btn-secondary">Secondary</button>
      <button type="button" class="btn disabled btn-success">Success</button>
      <button type="button" class="btn disabled btn-danger">Danger</button>
      <button type="button" class="btn disabled btn-warning">Warning</button>
      <button type="button" class="btn disabled btn-info">Info</button>
      <button type="button" class="btn disabled btn-dark">Dark</button>
    </app-card>
  </div>
  <div class="col-md-12">
    <app-card cardTitle="Rounded Button" [options]="false">
      <p>
        use
        <code>.btn-rounded</code>
        in class
        <code>.btn</code>
        class to get Rounded button
      </p>
      <button type="button" class="btn btn-rounded btn-primary">Primary</button>
      <button type="button" class="btn btn-rounded btn-secondary">Secondary</button>
      <button type="button" class="btn btn-rounded btn-success">Success</button>
      <button type="button" class="btn btn-rounded btn-danger">Danger</button>
      <button type="button" class="btn btn-rounded btn-warning">Warning</button>
      <button type="button" class="btn btn-rounded btn-info">Info</button>
      <button type="button" class="btn btn-rounded btn-dark">Dark</button>
      <hr />
      <p>
        use
        <code>.btn-rounded</code>
        in class
        <code>.btn-outline-*</code>
        class to get Rounded Outline button
      </p>
      <button type="button" class="btn btn-rounded btn-outline-primary">Primary</button>
      <button type="button" class="btn btn-rounded btn-outline-secondary">Secondary</button>
      <button type="button" class="btn btn-rounded btn-outline-success">Success</button>
      <button type="button" class="btn btn-rounded btn-outline-danger">Danger</button>
      <button type="button" class="btn btn-rounded btn-outline-warning">Warning</button>
      <button type="button" class="btn btn-rounded btn-outline-info">Info</button>
      <button type="button" class="btn btn-rounded btn-outline-dark">Dark</button>
    </app-card>
  </div>
  <div class="col-md-12">
    <app-card cardTitle="Glow Button" [options]="false">
      <p>
        use
        <code>.btn-glow</code>
        in class
        <code>.btn</code>
        class to get Glow button
      </p>
      <button type="button" class="btn btn-glow-primary btn-primary" title="btn btn-glow-primary btn-primary" data-toggle="tooltip">
        Primary
      </button>
      <button type="button" class="btn btn-glow-secondary btn-secondary" title="btn btn-glow-secondary btn-secondary" data-toggle="tooltip">
        Secondary
      </button>
      <button type="button" class="btn btn-glow-success btn-success" title="btn btn-glow-success btn-success" data-toggle="tooltip">
        Success
      </button>
      <button type="button" class="btn btn-glow-danger btn-danger" title="btn btn-glow-danger btn-danger" data-toggle="tooltip">
        Danger
      </button>
      <button type="button" class="btn btn-glow-warning btn-warning" title="btn btn-glow-warning btn-warning" data-toggle="tooltip">
        Warning
      </button>
      <button type="button" class="btn btn-glow-info btn-info" title="btn btn-glow-info btn-info" data-toggle="tooltip">Info</button>
      <button type="button" class="btn btn-glow-dark btn-dark" title="btn btn-glow-dark btn-dark" data-toggle="tooltip">Dark</button>
    </app-card>
  </div>
  <div class="col-md-12">
    <app-card cardTitle="Shadow Button" [options]="false">
      <p>
        use
        <code>.shadow-[1 / 2 / 3 / 4 / 5]</code>
        in class
        <code>.btn</code>
        class to get Shadow button
      </p>
      <button type="button" class="btn shadow-1 btn-primary">.shadow-1</button>
      <button type="button" class="btn shadow-2 btn-success">.shadow-2</button>
      <button type="button" class="btn shadow-3 btn-danger">.shadow-3</button>
      <button type="button" class="btn shadow-4 btn-warning">.shadow-4</button>
      <button type="button" class="btn shadow-5 btn-info">.shadow-5</button>
    </app-card>
  </div>
  <div class="col-md-6">
    <app-card cardTitle="Sizes [ Large ]" [options]="false">
      <p>
        use
        <code>.btn-lg</code>
        in class
        <code>.btn</code>
        class to get Large button
      </p>
      <button type="button" class="btn btn-primary btn-lg">Large button</button>
      <button type="button" class="btn btn-secondary btn-lg">Large button</button>
    </app-card>
  </div>
  <div class="col-md-6">
    <app-card cardTitle="Sizes [ small ]" [options]="false">
      <p>
        use
        <code>.btn-sm</code>
        in class
        <code>.btn</code>
        class to get Small button
      </p>
      <button type="button" class="btn btn-primary btn-sm">Small button</button>
      <button type="button" class="btn btn-secondary btn-sm">Small button</button>
    </app-card>
  </div>
  <!-- [ Checkbox button groups ] start -->
  <div class="col-md-6">
    <app-card cardTitle="Checkbox button groups" [options]="false">
      <div class="btn-group" role="group">
        <input type="checkbox" class="btn-check" id="btncheck1" autocomplete="off" />
        <label class="btn btn-outline-secondary" for="btncheck1">Checkbox 1</label>
        <input type="checkbox" class="btn-check" id="btncheck2" autocomplete="off" />
        <label class="btn btn-outline-secondary" for="btncheck2">Checkbox 2</label>
        <input type="checkbox" class="btn-check" id="btncheck3" autocomplete="off" />
        <label class="btn btn-outline-secondary" for="btncheck3">Checkbox 3</label>
      </div>
      <hr />
      <div class="btn-group" role="group">
        <input type="checkbox" class="btn-check" id="btnchecklite1" autocomplete="off" checked />
        <label class="btn btn-light-secondary" for="btnchecklite1">Checkbox 1</label>
        <input type="checkbox" class="btn-check" id="btnchecklite2" autocomplete="off" checked />
        <label class="btn btn-light-secondary" for="btnchecklite2">Checkbox 2</label>
        <input type="checkbox" class="btn-check" id="btnchecklite3" autocomplete="off" checked />
        <label class="btn btn-light-secondary" for="btnchecklite3">Checkbox 3</label>
      </div>
      <hr />
      <div class="btn-group" role="group">
        <input type="checkbox" class="btn-check" id="btnchecklitecol1" autocomplete="off" checked />
        <label class="btn btn-light-primary" for="btnchecklitecol1">Checkbox 1</label>
        <input type="checkbox" class="btn-check" id="btnchecklitecol2" autocomplete="off" checked />
        <label class="btn btn-light-success" for="btnchecklitecol2">Checkbox 2</label>
        <input type="checkbox" class="btn-check" id="btnchecklitecol3" autocomplete="off" checked />
        <label class="btn btn-light-danger" for="btnchecklitecol3">Checkbox 3</label>
      </div>
    </app-card>
  </div>
  <!-- [ Checkbox button groups ] end -->
  <!-- [ radio button groups ] start -->
  <div class="col-md-6">
    <app-card cardTitle="Radio button groups" [options]="false">
      <div class="btn-group" role="group">
        <input type="radio" class="btn-check" id="btnrdo1" autocomplete="off" name="btnradio1" />
        <label class="btn btn-outline-secondary" for="btnrdo1">Radio 1</label>
        <input type="radio" class="btn-check" id="btnrdo2" autocomplete="off" name="btnradio2" />
        <label class="btn btn-outline-secondary" for="btnrdo2">Radio 2</label>
        <input type="radio" class="btn-check" id="btnrdo3" autocomplete="off" name="btnradio32" />
        <label class="btn btn-outline-secondary" for="btnrdo3">Radio 3</label>
      </div>
      <hr />
      <div class="btn-group" role="group">
        <input type="radio" class="btn-check" id="btnrdolite1" autocomplete="off" name="btnradio1" checked />
        <label class="btn btn-light-secondary" for="btnrdolite1">Radio 1</label>
        <input type="radio" class="btn-check" id="btnrdolite2" autocomplete="off" name="btnradio2" checked />
        <label class="btn btn-light-secondary" for="btnrdolite2">Radio 2</label>
        <input type="radio" class="btn-check" id="btnrdolite3" autocomplete="off" name="btnradio23" checked />
        <label class="btn btn-light-secondary" for="btnrdolite3">Radio 3</label>
      </div>
      <hr />
      <div class="btn-group" role="group">
        <input type="radio" class="btn-check" id="btnrdolitecol1" autocomplete="off" name="btnradio31" checked />
        <label class="btn btn-light-primary" for="btnrdolitecol1">Radio 1</label>
        <input type="radio" class="btn-check" id="btnrdolitecol2" autocomplete="off" name="btnradio32" checked />
        <label class="btn btn-light-success" for="btnrdolitecol2">Radio 2</label>
        <input type="radio" class="btn-check" id="btnrdolitecol3" autocomplete="off" name="btnradio33" checked />
        <label class="btn btn-light-danger" for="btnrdolitecol3">Radio 3</label>
      </div>
    </app-card>
  </div>
  <!-- [ radio button groups ] end -->
  <div class="col-md-6">
    <app-card cardTitle="Buttons With Icon" [options]="false">
      <button type="button" class="btn btn-primary">
        <i class="feather icon-thumbs-up"></i>
        Primary
      </button>
      <button type="button" class="btn btn-secondary">
        <i class="feather icon-camera"></i>
        Secondary
      </button>
      <button type="button" class="btn btn-success">
        <i class="feather icon-check-circle"></i>
        Success
      </button>
      <button type="button" class="btn btn-danger">
        <i class="feather icon-slash"></i>
        Danger
      </button>
      <button type="button" class="btn btn-warning">
        <i class="feather icon-alert-triangle"></i>
        Warning
      </button>
      <button type="button" class="btn btn-info">
        <i class="feather icon-info"></i>
        Info
      </button>
    </app-card>
  </div>
  <div class="col-md-6">
    <app-card cardTitle="Outline Icon Buttons" [options]="false">
      <button type="button" class="btn btn-outline-primary">
        <i class="feather icon-thumbs-up"></i>
        Primary
      </button>
      <button type="button" class="btn btn-outline-secondary">
        <i class="feather icon-camera"></i>
        Secondary
      </button>
      <button type="button" class="btn btn-outline-success">
        <i class="feather icon-check-circle"></i>
        Success
      </button>
      <button type="button" class="btn btn-outline-danger">
        <i class="feather icon-slash"></i>
        Danger
      </button>
      <button type="button" class="btn btn-outline-warning">
        <i class="feather icon-alert-triangle"></i>
        Warning
      </button>
      <button type="button" class="btn btn-outline-info">
        <i class="feather icon-info"></i>
        Info
      </button>
    </app-card>
  </div>
  <div class="col-md-6">
    <app-card cardTitle="Only Icon" [options]="false">
      <button type="button" class="btn btn-icon btn-primary">
        <i class="feather icon-thumbs-up"></i>
      </button>
      <button type="button" class="btn btn-icon btn-secondary">
        <i class="feather icon-camera"></i>
      </button>
      <button type="button" class="btn btn-icon btn-success">
        <i class="feather icon-check-circle"></i>
      </button>
      <button type="button" class="btn btn-icon btn-danger">
        <i class="feather icon-slash"></i>
      </button>
      <button type="button" class="btn btn-icon btn-warning">
        <i class="feather icon-alert-triangle"></i>
      </button>
      <button type="button" class="btn btn-icon btn-info">
        <i class="feather icon-info"></i>
      </button>
    </app-card>
  </div>
  <div class="col-md-6">
    <app-card cardTitle="Outline Icon" [options]="false">
      <button type="button" class="btn btn-icon btn-outline-primary">
        <i class="feather icon-thumbs-up"></i>
      </button>
      <button type="button" class="btn btn-icon btn-outline-secondary">
        <i class="feather icon-camera"></i>
      </button>
      <button type="button" class="btn btn-icon btn-outline-success">
        <i class="feather icon-check-circle"></i>
      </button>
      <button type="button" class="btn btn-icon btn-outline-danger">
        <i class="feather icon-slash"></i>
      </button>
      <button type="button" class="btn btn-icon btn-outline-warning">
        <i class="feather icon-alert-triangle"></i>
      </button>
      <button type="button" class="btn btn-icon btn-outline-info">
        <i class="feather icon-info"></i>
      </button>
    </app-card>
  </div>
  <div class="col-md-6">
    <app-card cardTitle="Icon Button Rounded" [options]="false">
      <button type="button" class="btn btn-icon btn-rounded btn-primary">
        <i class="feather icon-thumbs-up"></i>
      </button>
      <button type="button" class="btn btn-icon btn-rounded btn-secondary">
        <i class="feather icon-camera"></i>
      </button>
      <button type="button" class="btn btn-icon btn-rounded btn-success">
        <i class="feather icon-check-circle"></i>
      </button>
      <button type="button" class="btn btn-icon btn-rounded btn-danger">
        <i class="feather icon-slash"></i>
      </button>
      <button type="button" class="btn btn-icon btn-rounded btn-warning">
        <i class="feather icon-alert-triangle"></i>
      </button>
      <button type="button" class="btn btn-icon btn-rounded btn-info">
        <i class="feather icon-info"></i>
      </button>
    </app-card>
  </div>
  <div class="col-md-6">
    <app-card cardTitle="Icon Outline Button Rounded" [options]="false">
      <button type="button" class="btn btn-icon btn-rounded btn-outline-primary">
        <i class="feather icon-thumbs-up"></i>
      </button>
      <button type="button" class="btn btn-icon btn-rounded btn-outline-secondary">
        <i class="feather icon-camera"></i>
      </button>
      <button type="button" class="btn btn-icon btn-rounded btn-outline-success">
        <i class="feather icon-check-circle"></i>
      </button>
      <button type="button" class="btn btn-icon btn-rounded btn-outline-danger">
        <i class="feather icon-slash"></i>
      </button>
      <button type="button" class="btn btn-icon btn-rounded btn-outline-warning">
        <i class="feather icon-alert-triangle"></i>
      </button>
      <button type="button" class="btn btn-icon btn-rounded btn-outline-info">
        <i class="feather icon-info"></i>
      </button>
    </app-card>
  </div>
  <div class="col-sm-12">
    <app-card cardTitle="Basic Dropdown Button" [options]="false">
      <p>
        use
        <code>ngbDropdown</code>
        <code>ngbDropdownToggle</code>
        <code>ngbDropdownMenu</code>
        selector in proper way to get dropdown button
      </p>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn btn-primary" ngbDropdownToggle type="button">Primary</button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn btn-secondary" ngbDropdownToggle type="button">Secondary</button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn btn-success" ngbDropdownToggle type="button">Success</button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn btn-danger" ngbDropdownToggle type="button">Danger</button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn btn-warning" ngbDropdownToggle type="button">Warning</button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn btn-info" ngbDropdownToggle type="button">Info</button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
    </app-card>
  </div>
  <div class="col-md-12">
    <app-card cardTitle="Split Dropdown Button" [options]="false">
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button type="button" class="btn btn-primary">Primary</button>
        <button type="button" class="btn btn-primary dropdown-toggle-split" ngbDropdownToggle>
          <span class="sr-only">Toggle Dropdown</span>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
          <div class="dropdown-divider"></div>
          <a class="dropdown-item" href="javascript:">Separated link</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button type="button" class="btn btn-secondary">Secondary</button>
        <button type="button" class="btn btn-secondary dropdown-toggle-split" ngbDropdownToggle>
          <span class="sr-only">Toggle Dropdown</span>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
          <div class="dropdown-divider"></div>
          <a class="dropdown-item" href="javascript:">Separated link</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button type="button" class="btn btn-success">Success</button>
        <button type="button" class="btn btn-success dropdown-toggle-split" ngbDropdownToggle>
          <span class="sr-only">Toggle Dropdown</span>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
          <div class="dropdown-divider"></div>
          <a class="dropdown-item" href="javascript:">Separated link</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button type="button" class="btn btn-danger">Danger</button>
        <button type="button" class="btn btn-danger dropdown-toggle-split" ngbDropdownToggle>
          <span class="sr-only">Toggle Dropdown</span>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
          <div class="dropdown-divider"></div>
          <a class="dropdown-item" href="javascript:">Separated link</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button type="button" class="btn btn-warning">Warning</button>
        <button type="button" class="btn btn-warning dropdown-toggle-split" ngbDropdownToggle>
          <span class="sr-only">Toggle Dropdown</span>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
          <div class="dropdown-divider"></div>
          <a class="dropdown-item" href="javascript:">Separated link</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button type="button" class="btn btn-info">Info</button>
        <button type="button" class="btn btn-info dropdown-toggle-split" ngbDropdownToggle>
          <span class="sr-only">Toggle Dropdown</span>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
          <div class="dropdown-divider"></div>
          <a class="dropdown-item" href="javascript:">Separated link</a>
        </div>
      </div>
    </app-card>
  </div>
  <div class="col-md-12">
    <app-card cardTitle="Basic Outline Dropdown Button" [options]="false">
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn btn-outline-primary" ngbDropdownToggle type="button">Primary</button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn btn-outline-secondary" ngbDropdownToggle type="button">Secondary</button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn btn-outline-success" ngbDropdownToggle type="button">Success</button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn btn-outline-danger" ngbDropdownToggle type="button">Danger</button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn btn-outline-warning" ngbDropdownToggle type="button">Warning</button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn btn-outline-info" ngbDropdownToggle type="button">Info</button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
    </app-card>
  </div>
  <div class="col-md-12">
    <app-card cardTitle="Split Outline Dropdown Button" [options]="false">
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button type="button" class="btn btn-outline-primary">Primary</button>
        <button type="button" class="btn btn-outline-primary dropdown-toggle-split" ngbDropdownToggle>
          <span class="sr-only">Toggle Dropdown</span>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
          <div class="dropdown-divider"></div>
          <a class="dropdown-item" href="javascript:">Separated link</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button type="button" class="btn btn-outline-secondary">Secondary</button>
        <button type="button" class="btn btn-outline-secondary dropdown-toggle-split" ngbDropdownToggle>
          <span class="sr-only">Toggle Dropdown</span>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
          <div class="dropdown-divider"></div>
          <a class="dropdown-item" href="javascript:">Separated link</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button type="button" class="btn btn-outline-success">Success</button>
        <button type="button" class="btn btn-outline-success dropdown-toggle-split" ngbDropdownToggle>
          <span class="sr-only">Toggle Dropdown</span>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
          <div class="dropdown-divider"></div>
          <a class="dropdown-item" href="javascript:">Separated link</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button type="button" class="btn btn-outline-danger">Danger</button>
        <button type="button" class="btn btn-outline-danger dropdown-toggle-split" ngbDropdownToggle>
          <span class="sr-only">Toggle Dropdown</span>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
          <div class="dropdown-divider"></div>
          <a class="dropdown-item" href="javascript:">Separated link</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button type="button" class="btn btn-outline-warning">Warning</button>
        <button type="button" class="btn btn-outline-warning dropdown-toggle-split" ngbDropdownToggle>
          <span class="sr-only">Toggle Dropdown</span>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
          <div class="dropdown-divider"></div>
          <a class="dropdown-item" href="javascript:">Separated link</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button type="button" class="btn btn-outline-info">Info</button>
        <button type="button" class="btn btn-outline-info dropdown-toggle-split" ngbDropdownToggle>
          <span class="sr-only">Toggle Dropdown</span>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
          <div class="dropdown-divider"></div>
          <a class="dropdown-item" href="javascript:">Separated link</a>
        </div>
      </div>
    </app-card>
  </div>
  <div class="col-md-6">
    <app-card cardTitle="Basic Icon Dropdown" [options]="false">
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-primary" ngbDropdownToggle type="button">
          <i class="feather icon-thumbs-up"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-secondary" ngbDropdownToggle type="button">
          <i class="feather icon-camera"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-success" ngbDropdownToggle type="button">
          <i class="feather icon-check-circle"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-danger" ngbDropdownToggle type="button">
          <i class="feather icon-slash"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-warning" ngbDropdownToggle type="button">
          <i class="feather icon-alert-triangle"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-info" ngbDropdownToggle type="button">
          <i class="feather icon-info"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
    </app-card>
  </div>
  <div class="col-md-6">
    <app-card cardTitle="Outline Icon Dropdown" [options]="false">
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-outline-primary" ngbDropdownToggle type="button">
          <i class="feather icon-thumbs-up"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-outline-secondary" ngbDropdownToggle type="button">
          <i class="feather icon-camera"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-outline-success" ngbDropdownToggle type="button">
          <i class="feather icon-check-circle"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-outline-danger" ngbDropdownToggle type="button">
          <i class="feather icon-slash"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-outline-warning" ngbDropdownToggle type="button">
          <i class="feather icon-alert-triangle"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-outline-info" ngbDropdownToggle type="button">
          <i class="feather icon-info"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
    </app-card>
  </div>
  <div class="col-md-6">
    <app-card cardTitle="Basic Rounded Icon Dropdown" [options]="false">
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-rounded btn-primary" ngbDropdownToggle type="button">
          <i class="feather icon-thumbs-up"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-rounded btn-secondary" ngbDropdownToggle type="button">
          <i class="feather icon-camera"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-rounded btn-success" ngbDropdownToggle type="button">
          <i class="feather icon-check-circle"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-rounded btn-danger" ngbDropdownToggle type="button">
          <i class="feather icon-slash"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-rounded btn-warning" ngbDropdownToggle type="button">
          <i class="feather icon-alert-triangle"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-rounded btn-info" ngbDropdownToggle type="button">
          <i class="feather icon-info"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
    </app-card>
  </div>
  <div class="col-md-6">
    <app-card cardTitle="Outline Rounded Icon Dropdown" [options]="false">
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-rounded btn-outline-primary" ngbDropdownToggle type="button">
          <i class="feather icon-thumbs-up"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-rounded btn-outline-secondary" ngbDropdownToggle type="button">
          <i class="feather icon-camera"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-rounded btn-outline-success" ngbDropdownToggle type="button">
          <i class="feather icon-check-circle"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-rounded btn-outline-danger" ngbDropdownToggle type="button">
          <i class="feather icon-slash"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-rounded btn-outline-warning" ngbDropdownToggle type="button">
          <i class="feather icon-alert-triangle"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
      <div class="btn-group mb-2 me-2" ngbDropdown [placement]="'bottom-left'">
        <button class="btn drp-icon btn-rounded btn-outline-info" ngbDropdownToggle type="button">
          <i class="feather icon-info"></i>
        </button>
        <div ngbDropdownMenu>
          <a class="dropdown-item" href="javascript:">Action</a>
          <a class="dropdown-item" href="javascript:">Another action</a>
          <a class="dropdown-item" href="javascript:">Something else here</a>
        </div>
      </div>
    </app-card>
  </div>
  <div class="col-md-6">
    <app-card cardTitle="Basic Button Group" [options]="false">
      <div class="btn-group mb-2" role="group" aria-label="Basic example">
        <button type="button" class="btn btn-secondary">Left</button>
        <button type="button" class="btn btn-secondary">Middle</button>
        <button type="button" class="btn btn-secondary">Right</button>
      </div>
    </app-card>
  </div>
  <div class="col-md-6">
    <app-card cardTitle="Button Toolbar" [options]="false">
      <div class="btn-toolbar">
        <div class="btn-group me-2 mb-1">
          <button type="button" class="btn btn-secondary">1</button>
          <button type="button" class="btn btn-secondary">2</button>
          <button type="button" class="btn btn-secondary">3</button>
          <button type="button" class="btn btn-secondary">4</button>
        </div>
        <div class="btn-group me-2 mb-1">
          <button type="button" class="btn btn-secondary">5</button>
          <button type="button" class="btn btn-secondary">6</button>
          <button type="button" class="btn btn-secondary">7</button>
        </div>
        <div class="btn-group mb-1">
          <button type="button" class="btn btn-secondary">8</button>
        </div>
      </div>
    </app-card>
  </div>
  <div class="col-md-12">
    <app-card cardTitle="Button Toolbar Size" [options]="false">
      <div class="row">
        <div class="col-xl-4 col-md-6 mb-2">
          <p>this is default size</p>
          <div class="btn-group" role="group" aria-label="button groups">
            <button type="button" class="btn btn-secondary">Left</button>
            <button type="button" class="btn btn-secondary">Middle</button>
            <button type="button" class="btn btn-secondary">Right</button>
          </div>
        </div>
        <div class="col-xl-4 col-md-12 mb-2">
          <p>
            use
            <code>.btn-group-lg</code>
            in class
            <code>.btn-group</code>
            class to get large size button group
          </p>
          <div class="btn-group btn-group-lg" role="group" aria-label="button groups xl">
            <button type="button" class="btn btn-secondary">Left</button>
            <button type="button" class="btn btn-secondary">Middle</button>
            <button type="button" class="btn btn-secondary">Right</button>
          </div>
        </div>
        <div class="col-xl-4 col-md-6 mb-2">
          <p>
            use
            <code>.btn-group-sm</code>
            in class
            <code>.btn-group</code>
            class to get small size button group
          </p>
          <div class="btn-group btn-group-sm" role="group" aria-label="button groups sm">
            <button type="button" class="btn btn-secondary">Left</button>
            <button type="button" class="btn btn-secondary">Middle</button>
            <button type="button" class="btn btn-secondary">Right</button>
          </div>
        </div>
      </div>
    </app-card>
  </div>
  <div class="col-md-6">
    <app-card cardTitle="Nesting" [options]="false">
      <div class="btn-group" role="group">
        <button type="button" class="btn btn-secondary">1</button>
        <button type="button" class="btn btn-secondary">2</button>
        <div class="btn-group" role="group" ngbDropdown>
          <button type="button" class="btn btn-secondary" ngbDropdownToggle>Dropdown</button>
          <div ngbDropdownMenu>
            <a class="dropdown-item" href="javascript:">Dropdown link</a>
            <a class="dropdown-item" href="javascript:">Dropdown link</a>
          </div>
        </div>
      </div>
    </app-card>
  </div>
  <div class="col-md-6">
    <app-card cardTitle="Vertical Variation" [options]="false">
      <div class="row">
        <div class="col-4">
          <div class="btn-group-vertical" role="group" aria-label="Button group with nested dropdown">
            <button type="button" class="btn m-0 btn-secondary">1</button>
            <button type="button" class="btn m-0 btn-secondary">2</button>
            <button type="button" class="btn m-0 btn-secondary">3</button>
          </div>
        </div>
        <div class="col-8">
          <div class="btn-group-vertical" role="group" aria-label="Button group with nested dropdown">
            <button type="button" class="btn m-0 btn-secondary">1</button>
            <button type="button" class="btn m-0 btn-secondary">2</button>
            <div class="btn-group" role="group">
              <div ngbDropdown>
                <button type="button" class="btn m-0 btn-secondary dropdown-toggle" data-bs-toggle="dropdown" ngbDropdownToggle>
                  Dropdown
                </button>
                <div class="dropdown-menu" ngbDropdownMenu>
                  <a class="dropdown-item" href="javascript:">Dropdown link</a>
                  <a class="dropdown-item" href="javascript:">Dropdown link</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </app-card>
  </div>
</div>
