import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { EthiopianDatepickerComponent } from './shared/components/ethiopian-datepicker/ethiopian-datepicker.component';

@Component({
  selector: 'app-test-ethiopian-calendar',
  standalone: true,
  imports: [CommonModule, FormsModule, EthiopianDatepickerComponent],
  template: `
    <div style="padding: 20px;">
      <h2>🇪🇹 Ethiopian Calendar Dropdown Test</h2>

      <div class="card" style="padding: 20px; border: 1px solid #ddd; border-radius: 8px; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <div style="margin-bottom: 20px;">
          <h4>Ethiopian Date Picker (Dropdown)</h4>
          <app-ethiopian-datepicker
            label="Ethiopian Expiration Date"
            [(ngModel)]="selectedDateValue"
            (dateChange)="onEthiopianDateChange($event)"
            [startYear]="2010"
            [endYear]="2040">
          </app-ethiopian-datepicker>
        </div>

        <div style="margin: 20px 0;" *ngIf="selectedDateValue">
          <h4>Selected Date Info:</h4>
          <div style="background-color: #e3f2fd; padding: 15px; border-radius: 8px;">
            <p><strong>Ethiopian:</strong><br>
            {{ selectedDateValue.ethiopian.day }} {{ selectedDateValue.ethiopian.monthName }} {{ selectedDateValue.ethiopian.year }}</p>

            <p><strong>Gregorian:</strong><br>
            {{ selectedDateValue.gregorian | date:'fullDate' }}</p>

            <p><strong>ISO:</strong> {{ selectedDateValue.iso }}</p>
          </div>
        </div>

        <div style="margin: 20px 0;">
          <h4>Features:</h4>
          <ul style="background-color: #e8f5e9; padding: 15px; border-radius: 8px;">
            <li>✅ Three separate dropdowns: Year, Month, Day</li>
            <li>✅ Amharic month names with English translations</li>
            <li>✅ Automatic day validation based on selected month</li>
            <li>✅ Leap year support for Pagumen (13th month)</li>
            <li>✅ Ethiopian to Gregorian date conversion</li>
            <li>✅ Angular form control integration</li>
            <li>✅ Responsive design</li>
          </ul>
        </div>

        <div style="margin: 20px 0;" *ngIf="testResults.length > 0">
          <h4>Test Log:</h4>
          <div style="background-color: #fff3e0; padding: 15px; border-radius: 8px; font-family: monospace;">
            <div *ngFor="let result of testResults" style="margin-bottom: 5px;">
              {{ result }}
            </div>
          </div>
        </div>
      </div>
    </div>
  `
})
export class TestEthiopianCalendarComponent {
  selectedDateValue: any = null;
  testResults: string[] = [];

  onEthiopianDateChange(dateValue: any) {
    console.log('Ethiopian date changed:', dateValue);
    this.selectedDateValue = dateValue;

    if (dateValue) {
      const timestamp = new Date().toLocaleTimeString();
      this.testResults.unshift(`[${timestamp}] Date selected: ${dateValue.ethiopian.day} ${dateValue.ethiopian.monthName} ${dateValue.ethiopian.year}`);

      // Keep only last 5 results
      if (this.testResults.length > 5) {
        this.testResults = this.testResults.slice(0, 5);
      }
    }
  }
}
