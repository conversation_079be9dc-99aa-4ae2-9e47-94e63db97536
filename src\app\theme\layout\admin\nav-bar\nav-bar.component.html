<header>
  <div class="m-header" [style.display]="this.headerStyle">
    <a class="mobile-menu" id="mobile-collapse1" href="javascript:" (click)="this.NavCollapsedMob.emit()"><span></span></a>
    <a [routerLink]="['/dashboard']" class="b-brand">
      <div style="background-color: #d2d3d2; width: 60px; height: 60px;" class="rounded-circle shadow-rounded d-flex align-items-center justify-content-center mt-2">
        <img width="50" height="50" src="../../../../../../assets/images/logo.png"/>
      </div>
      <span class="b-title">EBC</span>
    </a>
  </div>
  <a class="mobile-menu" [ngClass]="{ on: this.menuClass }" id="mobile-header" href="javascript:" (click)="toggleMobOption()">
    <i class="feather icon-more-horizontal"></i>
  </a>
  <div class="collapse navbar-collapse px-4" [style.display]="this.collapseStyle">
    <app-nav-left class="me-auto" [style.display]="this.headerStyle" />
    <app-nav-right class="ms-auto" />
  </div>
</header>
<div class="pc-menu-overlay" (click)="closeMenu()" (keydown)="handleKeyDown($event)" tabindex="0"></div>
