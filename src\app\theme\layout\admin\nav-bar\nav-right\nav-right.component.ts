// angular import
import { Component, inject } from '@angular/core';
import { Router } from '@angular/router';

// bootstrap import
import { NgbDropdownConfig } from '@ng-bootstrap/ng-bootstrap';
import { AuthService } from 'src/app/demo/pages/authentication/auth-service.service';
import { StorageService } from 'src/app/theme/shared/service/storage.service';

// project import
import { SharedModule } from 'src/app/theme/shared/shared.module';

@Component({
  selector: 'app-nav-right',
  imports: [SharedModule],
  templateUrl: './nav-right.component.html',
  styleUrls: ['./nav-right.component.scss'],
  providers: [NgbDropdownConfig]
})
export class NavRightComponent {
  // public props

  // constructor
  constructor(private authService: AuthService, private storage: StorageService,private router: Router) {
    const config = inject(NgbDropdownConfig);
    config.placement = 'bottom-right';
  }

  token:any = this.storage.getToken();
  profile:any;

  ngOnInit(): void {
    //Called after the constructor, initializing input properties, and the first call to ngOnChanges.
    //Add 'implements OnInit' to the class.
    this.GetProfile();
  }

  GetProfile() {
    this.authService.GetProfile(this.token).subscribe((res: any) => {
       this.profile = res.data;
    });
  }

  logout() {
    history.pushState(null, '', '/auth/signin');
    this.storage.logout();
    this.router.navigate(['/auth/signin']);
  }


}
