// angular import
import { Component, inject, output } from '@angular/core';
import { Location } from '@angular/common';

// project import
import { environment } from 'src/environments/environment';
import { NavigationItem, NavigationItems } from '../navigation';
import { SharedModule } from 'src/app/theme/shared/shared.module';
import { NavGroupComponent } from './nav-group/nav-group.component';
import PermissionService from 'src/app/theme/shared/service/permission.service';
import { StorageService } from 'src/app/theme/shared/service/storage.service';

@Component({
  selector: 'app-nav-content',
  imports: [SharedModule, NavGroupComponent],
  templateUrl: './nav-content.component.html',
  styleUrls: ['./nav-content.component.scss']
})
export class NavContentComponent {
  private location = inject(Location);

  // public method
  // version
  title = 'Demo application for version numbering';
  currentApplicationVersion = environment.appVersion;

  navigations!: NavigationItem[];
  wrapperWidth: number;
  windowWidth = window.innerWidth;
  permissions:any[] =[];
  NavCollapsedMob = output();
  public nav:any = NavigationItems;

  // constructor
  constructor(private service: PermissionService, private storage: StorageService) {
    // this.navigations = NavigationItems;

  }

  ngOnInit(): void {
    this.GetPermissions();

  }

  userId = this.storage.getUserId();

  fireOutClick() {
    let current_url = this.location.path();
    if (this.location['_baseHref']) {
      current_url = this.location['_baseHref'] + this.location.path();
    }
    const link = "a.nav-link[ href='" + current_url + "' ]";
    const ele = document.querySelector(link);
    if (ele !== null && ele !== undefined) {
      const parent = ele.parentElement;
      const up_parent = parent.parentElement.parentElement;
      const last_parent = up_parent.parentElement;
      if (parent.classList.contains('pcoded-hasmenu')) {
        parent.classList.add('pcoded-trigger');
        parent.classList.add('active');
      } else if (up_parent.classList.contains('pcoded-hasmenu')) {
        up_parent.classList.add('pcoded-trigger');
        up_parent.classList.add('active');
      } else if (last_parent.classList.contains('pcoded-hasmenu')) {
        last_parent.classList.add('pcoded-trigger');
        last_parent.classList.add('active');
      }
    }
  }

  filterNavMenu(menu: NavigationItem[], permissions: string[]): NavigationItem[] {
    return menu.reduce((filteredMenu, item) => {
      if (permissions.includes(item.id) || (item.children && item.children.length > 0)) {
        const newItem: NavigationItem = { ...item };
        if (item.children) {
          newItem.children = this.filterNavMenu(item.children, permissions);
        }
        filteredMenu.push(newItem);
      }
      return this.removeEmptyParents(filteredMenu, permissions);
    }, [] as NavigationItem[]);
  }

  removeEmptyParents(menu: NavigationItem[], permissions: string[]): NavigationItem[] {
    return menu.filter(item => {
      if (item.children && item.children.length > 0) {
        item.children = this.removeEmptyParents(item.children, permissions);
        return true;
      } else {
        return permissions.includes(item.id);
      }
    });
  }

  GetPermissions(): void {
    this.service.getPermissionsByUserId(Number(this.userId)).subscribe((per: any) => {
      let p = per.data;
      if (p.length > 0) {
        p.forEach((pers: any) => {
          this.permissions.push(pers.nav_menuId);
        });
        const resultArray = this.removeElementsFromArray(p,  this.permissions);
        this.navigations = this.filterNavMenu(this.nav, resultArray.length>0?resultArray: this.permissions);
      }

    });
  }


  removeElementsFromArray(array1, array2) {
    // Use filter to keep only the elements not present in array1
    const resultArray = array2.filter(element => array1.includes(element));
    return resultArray;
}
}
