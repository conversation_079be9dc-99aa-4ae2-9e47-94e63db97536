export interface NavigationItem {
  id: string;
  title: string;
  type: 'item' | 'collapse' | 'group';
  translate?: string;
  icon?: string;
  hidden?: boolean;
  url?: string;
  classes?: string;
  exactMatch?: boolean;
  external?: boolean;
  target?: boolean;
  breadcrumbs?: boolean;

  children?: NavigationItem[];
}
export const NavigationItems: NavigationItem[] = [
  {
    id: 'navigation',
    title: 'Navigation',
    type: 'group',
    icon: 'icon-navigation',
    children: [
      {
        id: 'dashboard',
        title: 'Dashboard',
        type: 'item',
        url: '/dashboard',
        icon: 'feather icon-home',
        classes: 'nav-item'
      }
    ]
  },
  {
    id: 'forms',
    title: 'Screen And Controls',
    type: 'group',
    icon: 'icon-group',
    children: [
      {
        id: 'screen-control',
        title: 'Screen Controls',
        type: 'item',
        url: '/screen-control',
        classes: 'nav-item',
        icon: 'feather icon-cast'
      },
    ]
  },
  {
    id: 'devices',
    title: 'Devices',
    type: 'group',
    icon: 'icon-group',
    children: [
      {
        id: 'devices',
        title: 'Devices',
        type: 'item',
        url: '/devices',
        classes: 'nav-item',
        icon: 'feather icon-monitor'
      },
    ]
  },
  {
    id: 'ui-element',
    title: 'Accounts and Settings',
    type: 'group',
    icon: 'icon-ui',
    children: [
      {
        id: 'basic',
        title: 'Settings',
        type: 'collapse',
        icon: 'feather icon-settings',
        children: [
          {
            id: 'permissions',
            title: 'Permissions',
            type: 'item',
            url: '/settings/permissions'
          },
          {
            id: 'accounts',
            title: 'Accounts',
            type: 'item',
            url: '/settings/accounts'
          },
          {
            id: 'change-password',
            title: 'Change Password',
            type: 'item',
            url: '/settings/change-password'
          },
        ]
      }
    ]
  },
  {
    id: 'ui-element',
    title: 'Ads and events',
    type: 'group',
    icon: 'icon-ui',
    children: [
      {
        id: 'Ads',
        title: 'Ads & Events',
        type: 'item',
        url: '/ads_events',
        classes: 'nav-item',
        icon: 'feather icon-activity'
      },
    ]
  }



];
