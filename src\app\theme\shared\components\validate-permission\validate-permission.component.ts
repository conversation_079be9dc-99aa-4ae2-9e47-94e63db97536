import { Component, EventEmitter, Input, Output } from '@angular/core';
import { SharedModule } from 'src/app/theme/shared/shared.module';
import { StorageService } from '../../service/storage.service';
import PermissionService from '../../service/permission.service';


@Component({
  selector: 'app-validate-permission',
  standalone: true,
  imports:[SharedModule],
  templateUrl: './validate-permission.component.html',
  styleUrls: ['./validate-permission.component.scss']
})
export default class ValidatePermissionComponent {
  @Input() pageId!: string; // Use nav_menuId as pageId
  isPageAllowed: boolean = false;
  isLoading: boolean = true;
  @Output() permissionChecked = new EventEmitter<boolean>();

  constructor(private pageService: PermissionService,private storage:StorageService) {}
  userId:any = this.storage.getUserId();
  token:any = this.storage.getToken();
  ngOnInit() {
    this.getPermissions();
  }

  async getPermissions(){
   await this.pageService.getPermissionsByUserId(this.userId).subscribe({
      next:(per:any) => {
        const allowedPages = per.data.map((item:any) => item.nav_menuId);
        this.isPageAllowed = allowedPages.includes(this.pageId);
        this.permissionChecked.emit(this.isPageAllowed);
        this.isLoading = false;
      },
      error:(err:any) => {
        console.log(err);
      }
    });
  }
}
