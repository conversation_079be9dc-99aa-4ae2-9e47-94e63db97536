import { Injectable, NgZone } from '@angular/core';
import { Router } from '@angular/router';
import { fromEvent, merge, timer, Subscription } from 'rxjs';
import { switchMap, tap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class InactivityService {
  private inactivityTimeout = 30 * 60 * 1000; // 30 minutes
  private activitySubscription!: Subscription;

  constructor(private router: Router, private ngZone: NgZone) {}

  public startWatching(): void {
    this.ngZone.runOutsideAngular(() => {
      const mouseMove$ = fromEvent(document, 'mousemove');
      const keyPress$ = fromEvent(document, 'keypress');
      const mouseClick$ = fromEvent(document, 'click');

      // Merging all events
      const activity$ = merge(mouseMove$, keyPress$, mouseClick$);

      this.activitySubscription = activity$
        .pipe(
          tap(() => console.log('Activity detected')),
          switchMap(() => timer(this.inactivityTimeout))
        )
        .subscribe(() => {
          this.logout();
        });
    });
  }

  public stopWatching(): void {
    if (this.activitySubscription) {
      this.activitySubscription.unsubscribe();
    }
  }


  logout():void{
    history.pushState(null, '', '/');
    localStorage.clear();
    this.router.navigate(['/']);
   }
}
