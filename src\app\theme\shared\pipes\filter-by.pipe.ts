import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'filterBy',
  standalone: true
})
export class FilterByPipe implements PipeTransform {
  transform(items: any[], property: string[], value: any): any[] {
    if (!items || !property || value === undefined) {
      return items;
    }

    return items.filter(item => {
      let itemValue = item;
      
      // Navigate through nested properties
      for (const prop of property) {
        if (itemValue && itemValue.hasOwnProperty(prop)) {
          itemValue = itemValue[prop];
        } else {
          itemValue = undefined;
          break;
        }
      }
      
      return itemValue === value;
    });
  }
}
