import { Injectable } from '@angular/core';
import { NavigationItem, NavigationItems } from '../../layout/admin/navigation/navigation';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class NavigationFilterNewService {
  // Original navigation items
  private originalNavigationItems = [...NavigationItems];

  // Filtered navigation items
  private filteredNavigationSubject = new BehaviorSubject<NavigationItem[]>([]);
  filteredNavigation$ = this.filteredNavigationSubject.asObservable();

  // Current user permissions
  private userPermissions: string[] = [];

  constructor() {
    // Initialize with default navigation (just dashboard)
    console.log('NavigationFilterNewService - Initializing with default navigation');
    this.setDefaultNavigation();
  }

  /**
   * Set default navigation with just dashboard
   */
  setDefaultNavigation(): void {
    console.log('NavigationFilterNewService - Setting default navigation');

    // Set default permissions for dashboard and navigation
    this.userPermissions = ['dashboard', 'navigation'];

    // Filter navigation based on permissions
    this.filterNavigation();
  }

  /**
   * Set user permissions and filter navigation
   */
  setPermissions(permissions: any[]): void {
    console.log('NavigationFilterNewService - Setting permissions:', permissions);

    // Extract menu IDs from permissions
    this.userPermissions = [];

    // Check if permissions is an array of objects with nav_menuId property
    if (permissions && permissions.length > 0) {
      if (typeof permissions[0] === 'object') {
        if ('nav_menuId' in permissions[0]) {
          this.userPermissions = permissions.map(p => p.nav_menuId);
        } else if ('menuId' in permissions[0]) {
          this.userPermissions = permissions.map(p => p.menuId);
        } else if ('id' in permissions[0]) {
          this.userPermissions = permissions.map(p => p.id);
        }
      } else if (Array.isArray(permissions) && typeof permissions[0] === 'string') {
        // If permissions is just an array of strings
        this.userPermissions = permissions;
      }
    } else {
      // If no permissions provided, use empty permissions
      console.log('NavigationFilterNewService - No permissions provided, using empty permissions');
      this.userPermissions = [];
    }

    console.log('NavigationFilterNewService - Extracted permission IDs:', this.userPermissions);

    // Filter navigation based on permissions
    this.filterNavigation();
  }

  /**
   * Filter navigation menu based on permissions
   */
  private filterNavigation(): void {
    console.log('NavigationFilterNewService - Filtering navigation with permissions:', this.userPermissions);

    // No longer automatically adding dashboard and navigation permissions
    // Only use the permissions explicitly granted to the user

    // Filter navigation based on permissions
    const filteredNav = this.filterNavMenu([...this.originalNavigationItems], this.userPermissions);
    console.log('NavigationFilterNewService - Filtered navigation:', filteredNav);

    // If filtered navigation is empty, provide at least dashboard
    if (!filteredNav || filteredNav.length === 0) {
      console.log('NavigationFilterNewService - Filtered navigation is empty, showing minimal navigation');

      // Create a minimal navigation with just the dashboard
      const minimalNavigation = this.originalNavigationItems.filter(item =>
        item.id === 'navigation' || // Keep the navigation group
        (item.children && item.children.some(child => child.id === 'dashboard')) // Keep groups containing dashboard
      );

      // Further filter children to only include dashboard
      const defaultNav = minimalNavigation.map(group => {
        if (group.children) {
          return {
            ...group,
            children: group.children.filter(item => item.id === 'dashboard')
          };
        }
        return group;
      }).filter(group => group.children && group.children.length > 0);

      this.filteredNavigationSubject.next(defaultNav);
      return;
    }

    this.filteredNavigationSubject.next(filteredNav);
  }

  /**
   * Filter navigation menu recursively
   */
  filterNavMenu(menu: any[], permissions: string[]): any[] {
    return menu.reduce((filteredMenu, item) => {
      if (permissions.includes(item.id) || (item.children && item.children.length > 0)) {
        const newItem: any = { ...item };
        if (item.children) {
          newItem.children = this.filterNavMenu(item.children, permissions);
        }
        filteredMenu.push(newItem);
      }
      return this.removeEmptyParents(filteredMenu, permissions);
    }, [] as any[]);
  }

  /**
   * Remove empty parent items
   */
  removeEmptyParents(menu: any[], permissions: string[]): any[] {
    return menu.filter(item => {
      if (item.children && item.children.length > 0) {
        item.children = this.removeEmptyParents(item.children, permissions);
        return item.children.length > 0;
      } else {
        return permissions.includes(item.id);
      }
    });
  }

  /**
   * Get current filtered navigation
   */
  getFilteredNavigation(): NavigationItem[] {
    return this.filteredNavigationSubject.value;
  }

  /**
   * Reset navigation to original state
   */
  resetNavigation(): void {
    this.userPermissions = [];
    this.filteredNavigationSubject.next([...this.originalNavigationItems]);
  }

  /**
   * Check if user has a specific permission
   */
  hasPermission(menuId: string): boolean {
    // If no permissions, return false
    if (!this.userPermissions || this.userPermissions.length === 0) {
      return false;
    }

    return this.userPermissions.includes(menuId);
  }

  /**
   * Get all user permissions
   */
  getUserPermissions(): string[] {
    return [...this.userPermissions];
  }
}
