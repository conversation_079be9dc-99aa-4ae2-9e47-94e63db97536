import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { NavigationFilterNewService } from './navigation-filter-new.service';

@Injectable({
  providedIn: 'root'
})
export class NavigationService {
  // Subject to emit navigation refresh events
  private refreshNavigationSource = new Subject<void>();

  // Observable that components can subscribe to
  refreshNavigation$ = this.refreshNavigationSource.asObservable();

  constructor(private navigationFilterNewService: NavigationFilterNewService) { }

  // Method to trigger navigation refresh
  refreshNavigation(): void {
    console.log('NavigationService - Refresh navigation triggered');

    // Emit the event immediately without delay
    console.log('NavigationService - Emitting refresh event');
    this.refreshNavigationSource.next();
  }
}
