import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from '../../../../environments/environment';
import { StorageService } from './storage.service';
import { Observable, catchError, of } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export default class PermissionService {

  constructor(private http: HttpClient, private storageService: StorageService) { }

  // Helper method to create HTTP options with auth token
  private getHttpOptions() {
    const token = this.storageService.getToken();
    return {
      headers: new HttpHeaders({
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`
      })
    };
  }

  // Get all permissions
  getAllPermissions() {
    return this.http.get<any>(
      environment.URL + `/permissions`,
      this.getHttpOptions()
    );
  }

  // Get a permission by ID
  getPermissionById(id: number) {
    return this.http.get<any>(
      environment.URL + `/permissions/${id}`,
      this.getHttpOptions()
    );
  }

  // Get permissions by user ID
  getPermissionsByUserId(userId: number): Observable<any> {
    // Check if userId is valid
    if (!userId) {
      console.error('Invalid userId provided to getPermissionsByUserId:', userId);
      return of({ status: 404, data: [] }); // Return empty data with 404 status
    }

    return this.http.get<any>(
      environment.URL + `/permissions/user/${userId}`,
      this.getHttpOptions()
    ).pipe(
      catchError(error => {
        console.error('Error fetching permissions for user:', error);
        return of({ status: error.status || 500, data: [] }); // Return empty data with error status
      })
    );
  }

  // Get permissions by menu ID
  getPermissionsByMenuId(menuId: string) {
    return this.http.get<any>(
      environment.URL + `/permissions/menu/${menuId}`,
      this.getHttpOptions()
    );
  }

  // Create a new permission
  // createPermission(data: { userId: number, nav_menuId: string }) {
  //   return this.http.post<any>(
  //     environment.URL + `/permissions`,
  //     data,
  //     this.getHttpOptions()
  //   );
  // }

  // Create multiple permissions for a user
  createMultiplePermissions(data: { userId: number, Menu: string[] }) {
    return this.http.post<any>(
      environment.URL + `/permissions/multiple`,
      data,
      this.getHttpOptions()
    );
  }

  // Update a permission
  updatePermission(id: number, data: { userId: number, nav_menuId: string }) {
    return this.http.put<any>(
      environment.URL + `/permissions/${id}`,
      data,
      this.getHttpOptions()
    );
  }

  // Delete a permission by ID
  deletePermission(id: number) {
    return this.http.delete<any>(
      environment.URL + `/permissions/${id}`,
      this.getHttpOptions()
    );
  }

  // Delete a permission by user ID and menu ID
  deletePermissionByUserAndMenu(userId: number, menuId: string) {
    return this.http.delete<any>(
      environment.URL + `/permissions/user/${userId}/menu/${menuId}`,
      this.getHttpOptions()
    );
  }
}
