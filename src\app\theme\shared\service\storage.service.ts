import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class StorageService {

  constructor() { }


  setUserId(userId: any):void{
    localStorage.setItem('userId', userId);
  }



  getUserId(){
    let userId = localStorage.getItem('userId');
    return userId;
  }

 setToken(token:any):void{
  localStorage.setItem('token', token);
 }

 getToken(){
  let token = localStorage.getItem('token');
  return token;
 }

 logout(){
  localStorage.clear();
 }

}
