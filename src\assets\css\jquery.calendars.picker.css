/* Ethiopian Calendar Picker Styles */

.calendars-dp {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 0;
  margin: 0;
  z-index: 1000;
}

.calendars-dp-popup {
  position: absolute;
  display: none;
}

.calendars-dp-inline {
  position: static;
  display: block;
}

.calendars-dp-header {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 12px 16px;
  border-radius: 8px 8px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.calendars-dp-nav {
  display: flex;
  align-items: center;
  gap: 8px;
}

.calendars-dp-nav a {
  color: white;
  text-decoration: none;
  padding: 6px 10px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
  font-weight: 500;
}

.calendars-dp-nav a:hover {
  background: rgba(255, 255, 255, 0.2);
}

.calendars-dp-month-year {
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.calendars-dp-calendar {
  padding: 16px;
}

.calendars-dp-calendar table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
}

.calendars-dp-calendar th {
  background: #f8f9fa;
  color: #495057;
  font-weight: 600;
  text-align: center;
  padding: 8px 4px;
  border: 1px solid #e9ecef;
  font-size: 12px;
}

.calendars-dp-calendar td {
  text-align: center;
  padding: 0;
  border: 1px solid #e9ecef;
}

.calendars-dp-calendar td a {
  display: block;
  padding: 8px 4px;
  text-decoration: none;
  color: #495057;
  transition: all 0.3s ease;
  border-radius: 4px;
  margin: 2px;
}

.calendars-dp-calendar td a:hover {
  background: #e3f2fd;
  color: #1976d2;
}

.calendars-dp-calendar .calendars-dp-selected a {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-weight: 600;
}

.calendars-dp-calendar .calendars-dp-today a {
  background: #fff3cd;
  color: #856404;
  border: 2px solid #ffc107;
}

.calendars-dp-calendar .calendars-dp-other-month a {
  color: #adb5bd;
}

.calendars-dp-calendar .calendars-dp-unselectable a {
  color: #dee2e6;
  cursor: not-allowed;
}

.calendars-dp-calendar .calendars-dp-week-col {
  background: #f8f9fa;
  color: #6c757d;
  font-size: 11px;
  font-weight: 500;
}

/* Ethiopian specific styles */
.ethiopian-datepicker {
  direction: ltr;
}

.ethiopian-datepicker .calendars-dp-month-year {
  font-family: 'Noto Sans Ethiopic', 'Segoe UI', sans-serif;
}

.ethiopian-datepicker .calendars-dp-calendar th,
.ethiopian-datepicker .calendars-dp-calendar td a {
  font-family: 'Noto Sans Ethiopic', 'Segoe UI', sans-serif;
}

/* Modern input styling */
.ethiopian-date-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  font-family: 'Noto Sans Ethiopic', 'Segoe UI', sans-serif;
  background: #fff;
  transition: all 0.3s ease;
  outline: none;
}

.ethiopian-date-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.ethiopian-date-input:hover {
  border-color: #adb5bd;
}

/* Responsive design */
@media (max-width: 768px) {
  .calendars-dp {
    font-size: 12px;
  }
  
  .calendars-dp-header {
    padding: 10px 12px;
  }
  
  .calendars-dp-calendar {
    padding: 12px;
  }
  
  .calendars-dp-calendar td a {
    padding: 6px 2px;
    font-size: 12px;
  }
}

/* Animation for popup */
.calendars-dp-popup {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Close button */
.calendars-dp-close {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

.calendars-dp-close:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Navigation buttons */
.calendars-dp-prev,
.calendars-dp-next {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-size: 14px;
}

.calendars-dp-prev:hover,
.calendars-dp-next:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Month/Year dropdowns */
.calendars-dp-month-year select {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #333;
  padding: 4px 8px;
  border-radius: 4px;
  margin: 0 4px;
  font-family: 'Noto Sans Ethiopic', 'Segoe UI', sans-serif;
}

/* Today button */
.calendars-dp-today-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin: 8px;
  transition: background-color 0.3s ease;
}

.calendars-dp-today-btn:hover {
  background: #218838;
}

/* Clear button */
.calendars-dp-clear-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin: 8px;
  transition: background-color 0.3s ease;
}

.calendars-dp-clear-btn:hover {
  background: #c82333;
}
