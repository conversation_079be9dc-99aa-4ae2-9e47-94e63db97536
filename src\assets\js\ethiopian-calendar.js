/* http://keith-wood.name/calendars.html
   Ethiopian calendar for jQuery v2.2.0.
   Written by <PERSON> (kbwood.au{at}gmail.com) February 2010.
   Available under the MIT (http://keith-wood.name/licence.html) license.
   Please attribute the author if you use it. */

(function($) { // Hide scope, no $ conflict
	'use strict';

	/** Implementation of the Ethiopian calendar.
		See also http://en.wikipedia.org/wiki/Ethiopian_calendar.
		@class EthiopianCalendar
		@param {string} [language=''] The language code (default English) for localisation. */
	function EthiopianCalendar(language) {
		this.local = this.regionalOptions[language || ''] || this.regionalOptions[''];
	}

	EthiopianCalendar.prototype = new $.calendars.baseCalendar();

	// Ensure proper inheritance
	EthiopianCalendar.prototype.constructor = EthiopianCalendar;

	$.extend(EthiopianCalendar.prototype, {
		/** The calendar name.
			@memberof EthiopianCalendar */
		name: 'Ethiopian',
		/** Julian date of start of Ethiopian epoch: 29 August 8 CE (Gregorian).
			@memberof EthiopianCalendar */
		jdEpoch: 1724220.5,
		/** Days per month in a common year.
			@memberof EthiopianCalendar */
		daysPerMonth: [30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5],
		/** `true` if has a year zero, `false` if not.
			@memberof EthiopianCalendar */
		hasYearZero: false,
		/** The minimum month number.
			@memberof EthiopianCalendar */
		minMonth: 1,
		/** The first month in the year.
			@memberof EthiopianCalendar */
		firstMonth: 1,
		/** The minimum day number.
			@memberof EthiopianCalendar */
		minDay: 1,

		/** Localisations for the plugin.
			Entries are objects indexed by the language code ('' being the default US/English).
			Each object has the following attributes.
			@memberof EthiopianCalendar
			@property {string} name The calendar name.
			@property {string[]} epochs The epoch names (before/after year 0).
			@property {string[]} monthNames The long names of the months of the year.
			@property {string[]} monthNamesShort The short names of the months of the year.
			@property {string[]} dayNames The long names of the days of the week.
			@property {string[]} dayNamesShort The short names of the days of the week.
			@property {string[]} dayNamesMin The minimal names of the days of the week.
			@property {string} dateFormat The date format for this calendar.
				See the options on <a href="BaseCalendar.html#formatDate"><code>formatDate</code></a> for details.
			@property {number} firstDay The number of the first day of the week, starting at 0.
			@property {boolean} isRTL `true` if this localisation reads right-to-left. */
		regionalOptions: { // Available regional settings, indexed by language/country code
			'': { // Default regional settings - English/US
				name: 'Ethiopian',
				epochs: ['BEE', 'EE'],
				monthNames: ['Meskerem', 'Tikemet', 'Hidar', 'Tahesas', 'Tir', 'Yekatit',
				'Megabit', 'Miazia', 'Genbot', 'Sene', 'Hamle', 'Nehase', 'Pagume'],
				monthNamesShort: ['Mes', 'Tik', 'Hid', 'Tah', 'Tir', 'Yek',
				'Meg', 'Mia', 'Gen', 'Sen', 'Ham', 'Neh', 'Pag'],
				dayNames: ['Ehud', 'Segno', 'Maksegno', 'Erob', 'Hamus', 'Arb', 'Kidame'],
				dayNamesShort: ['Ehu', 'Seg', 'Mak', 'Ero', 'Ham', 'Arb', 'Kid'],
				dayNamesMin: ['Eh', 'Se', 'Ma', 'Er', 'Ha', 'Ar', 'Ki'],
				dateFormat: 'dd/mm/yyyy',
				firstDay: 0,
				isRTL: false
			}
		},

		/** Determine whether this date is in a leap year.
			@memberof EthiopianCalendar
			@param {CDate|number} year The date to examine or the year to examine.
			@return {boolean} `true` if this is a leap year, `false` if not.
			@throws Error if an invalid year or a different calendar is used.
			@example if (calendar.leapYear(date)) ...
			if (calendar.leapYear(2014)) ... */
		leapYear: function(year) {
			var date = this._validate(year, this.minMonth, this.minDay, $.calendars.local.invalidYear);
			var year = date.year() + (date.year() < 0 ? 1 : 0); // No year zero
			return year % 4 === 3 || year % 4 === -1;
		},

		/** Retrieve the current date in this calendar.
			@memberof EthiopianCalendar
			@return {CDate} Today's date.
			@example var today = calendar.today() */
		today: function() {
			return this.fromJSDate(new Date());
		},

		/** Retrieve the number of months in a year.
			@memberof EthiopianCalendar
			@param {CDate|number} year The date to examine or the year to examine.
			@return {number} The number of months.
			@throws Error if an invalid year or a different calendar is used.
			@example var months = calendar.monthsInYear(date)
			var months = calendar.monthsInYear(2014) */
		monthsInYear: function(year) {
			this._validate(year, this.minMonth, this.minDay, $.calendars.local.invalidYear);
			return 13;
		},

		/** Retrieve the number of days in a year.
			@memberof EthiopianCalendar
			@param {CDate|number} year The date to examine or the year to examine.
			@return {number} The number of days.
			@throws Error if an invalid year or a different calendar is used.
			@example var days = calendar.daysInYear(date)
			var days = calendar.daysInYear(2014) */
		daysInYear: function(year) {
			return (this.leapYear(year) ? 366 : 365);
		},

		/** Retrieve the number of days in a month.
			@memberof EthiopianCalendar
			@param {CDate|number} year The date to examine or the year to examine.
			@param {number} [month] The month to examine (if numeric `year` specified above).
			@return {number} The number of days in this month.
			@throws Error if an invalid month/year or a different calendar is used.
			@example var days = calendar.daysInMonth(date)
			var days = calendar.daysInMonth(2014, 1) */
		daysInMonth: function(year, month) {
			var date = this._validate(year, month, this.minDay, $.calendars.local.invalidMonth);
			return this.daysPerMonth[date.month() - 1] +
				(date.month() === 13 && this.leapYear(date.year()) ? 1 : 0);
		},

		/** Determine whether this date is a week day.
			@memberof EthiopianCalendar
			@param {CDate|number} year The date to examine or the year to examine.
			@param {number} [month] The month to examine (if numeric `year` specified above).
			@param {number} [day] The day to examine (if numeric `year` specified above).
			@return {boolean} `true` if a week day, `false` if not.
			@throws Error if an invalid date or a different calendar is used.
			@example if (calendar.weekDay(date)) ...
			if (calendar.weekDay(2014, 1, 26)) ... */
		weekDay: function(year, month, day) {
			return (this.dayOfWeek(year, month, day) || 7) < 6;
		},

		/** Retrieve the Julian date equivalent for this date,
			i.e. days since January 1, 4713 BCE Greenwich noon.
			@memberof EthiopianCalendar
			@param {CDate|number} year The date to convert or the year to convert.
			@param {number} [month] The month to convert (if numeric `year` specified above).
			@param {number} [day] The day to convert (if numeric `year` specified above).
			@return {number} The equivalent Julian date.
			@throws Error if an invalid date or a different calendar is used.
			@example var jd = calendar.toJD(date)
			var jd = calendar.toJD(2014, 1, 26) */
		toJD: function(year, month, day) {
			var date = this._validate(year, month, day, $.calendars.local.invalidDate);
			year = date.year();
			if (year < 0) { year++; } // No year zero
			return date.day() + (date.month() - 1) * 30 +
				(year - 1) * 365 + Math.floor(year / 4) + this.jdEpoch - 1;
		},

		/** Create a new date from a Julian date.
			@memberof EthiopianCalendar
			@param {number} jd The Julian date to convert.
			@return {CDate} The equivalent date.
			@example var date = calendar.fromJD(jd) */
		fromJD: function(jd) {
			var c = Math.floor(jd) + 0.5 - this.jdEpoch;
			var year = Math.floor((c - Math.floor((c + 366) / 1461)) / 365) + 1;
			if (year <= 0) { year--; } // No year zero
			c = Math.floor(jd) + 0.5 - this.newDate(year, 1, 1).toJD();
			var month = Math.floor(c / 30) + 1;
			var day = c - (month - 1) * 30 + 1;
			return this.newDate(year, month, day);
		}
	});

	// Ethiopian calendar implementation
	$.calendars.calendars.ethiopian = EthiopianCalendar;

})(jQuery);
