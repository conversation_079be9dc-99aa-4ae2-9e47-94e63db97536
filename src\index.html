<!doctype html>
<html lang="en">
  <head>
    <title>Dashboard | Smart Screen Control</title>

    <!-- HTML5 Shim and Respond.js IE11 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 11]>
      <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
      <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->

    <base href="/" />

    <!-- Meta -->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=0, minimal-ui" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta
      name="description"
      content="Datta able angular 19+ admin template made using bootstrap 5 and it has huge amount of ready made feature, UI components, pages which completely fulfills any dashboard needs."
    />
    <meta
      name="keywords"
      content="Admin templates, Bootstrap Admin templates, angular 19+, bootstrap 5, Dashboard, Dashboard Templates, sass admin templates, html admin templates, Responsive, Bootstrap Admin templates free download, Angular19 Admin templates free download,premium Bootstrap Admin templates,premium Angular19 Admin templates download"
    />
    <meta name="author" content="Codedthemes" />

    <link rel="icon" type="image/x-icon" href="./assets/images/logo.png" />
    <link href="./assets/cute-alert-master/style.css" rel="stylesheet" />
    <!-- Ethiopian Calendar CSS -->
    <link href="./assets/css/jquery.calendars.picker.css" rel="stylesheet" />
    <!-- font style -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600" rel="stylesheet" />
    <!-- Ethiopian Calendar Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Ethiopic:wght@400;500;600;700&display=swap" rel="stylesheet" />
  </head>
  <body>
    <app-root></app-root>
    <script src="./assets/cute-alert-master/cute-alert.js"></script>
    <!-- Ethiopian Calendar Scripts -->
    <!-- <script src="./assets/js/jquery.calendars.js"></script>
    <script src="./assets/js/ethiopian-calendar.js"></script>
    <script src="./assets/js/jquery.calendars.ethiopian-am.js"></script>
    <script src="./assets/js/jquery.calendars.picker.js"></script> -->
  </body>
</html>
