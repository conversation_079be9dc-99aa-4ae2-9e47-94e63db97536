
@use '@angular/material' as mat;

@include mat.core();



@import 'scss/variables';

/* fonts-icon */
@import 'scss/fonts/fontawesome/scss/fontawesome';
@import "~ng-zorro-antd/ng-zorro-antd.min.css";
@import 'scss/fonts/feather/iconfont';

@import 'scss/general';
@import 'scss/generic';
@import 'scss/mixins/function';

/* important element */
@import 'scss/menu/menu-lite';

/* basic elements */
@import 'scss/theme-elements/theme-elements';

/* third party modules style*/
@import 'scss/plugins/plugins';

@import 'scss/custom';

/* Fix for black boxes in icons */
.icon-box,
.feather,
i[class*="icon-"],
i[class*="feather"] {
  background-color: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;

  &::before,
  &::after {
    background: transparent !important;
    box-shadow: none !important;
    border: none !important;
  }
}

.icon-box {
  background: rgba(255, 255, 255, 0.2) !important;
}
